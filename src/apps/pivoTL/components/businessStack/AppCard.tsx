import React from 'react';
import { AppCardProps } from '../../types/businessStack';

export const AppCard: React.FC<AppCardProps> = ({
  app,
  onConnect,
  onOpenOutlookOrganisationModal,
  onOpenOutlookPersonalModal,
  onOpenSharePointModal,
  onOpenSalesforceModal,
  onOpenTwilioModal,
}) => {
  const handleConnect = () => {
    if (!app.isConnected) {
      if (
        app.name === 'Outlook (Organisation)' &&
        onOpenOutlookOrganisationModal
      ) {
        onOpenOutlookOrganisationModal(app.name);
      } else if (
        app.name === 'Outlook (Personal)' &&
        onOpenOutlookPersonalModal
      ) {
        onOpenOutlookPersonalModal();
      } else if (app.name === 'SharePoint' && onOpenSharePointModal) {
        onOpenSharePointModal();
      } else if (app.name === 'Salesforce' && onOpenSalesforceModal) {
        onOpenSalesforceModal();
      } else if (app.name === 'Twilio' && onOpenTwilioModal) {
        onOpenTwilioModal();
      } else {
        // Use default connection flow for other apps
        onConnect(app.name);
      }
    }
  };

  return (
    <div className="group flex flex-col items-center">
      {/* App Name */}
      <h3
        className="mb-2 cursor-pointer text-center text-sm font-bold text-blackThree transition-colors group-hover:text-primary md:text-nowrap"
        onClick={handleConnect}
      >
        {app.name}
      </h3>

      {/* App Logo */}
      <div
        className="mb-2.5 h-28 w-28 cursor-pointer rounded-md border-primary bg-grayTwentySeven p-6 shadow-sm transition-all group-hover:border-2 group-hover:bg-blueTwo group-hover:shadow-md"
        onClick={handleConnect}
      >
        <div className="h-15 w-15 flex-shrink-0">
          <img
            src={app.logo}
            alt={`${app.name} logo`}
            className="h-full w-full rounded-lg object-contain"
            onError={e => {
              // Fallback for broken images
              const target = e.target as HTMLImageElement;
              target.src =
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjQgMjRIMzJWMzJIMjRWMjRaIiBmaWxsPSIjRDFEOUUwIi8+CjxwYXRoIGQ9Ik0zMiAyNEg0MFYzMkgzMlYyNFoiIGZpbGw9IiNEMUQ5RTAiLz4KPHBhdGggZD0iTTI0IDMySDMyVjQwSDI0VjMyWiIgZmlsbD0iI0QxRDlFMCIvPgo8cGF0aCBkPSJNMzIgMzJINDBWNDBIMzJWMzJaIiBmaWxsPSIjRDFEOUUwIi8+Cjwvc3ZnPgo=';
            }}
          />
        </div>
      </div>

      {/* Connection Button */}
      <button
        onClick={handleConnect}
        disabled={app.isConnected}
        className={`flex h-10 w-32 items-center justify-center rounded-full border border-blackOne px-8 py-2 font-medium transition-colors group-hover:border-none ${
          app.isConnected
            ? 'cursor-not-allowed bg-blackOne text-white group-hover:cursor-not-allowed'
            : 'group-hover:bg-primary group-hover:text-white'
        }`}
      >
        <span>{app.isConnected ? 'Connected' : 'Connect'}</span>
      </button>
    </div>
  );
};
