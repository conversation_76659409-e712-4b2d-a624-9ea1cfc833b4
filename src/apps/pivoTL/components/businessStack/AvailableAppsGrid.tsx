import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import { AppCard } from './AppCard';
import { AvailableAppsGridProps } from '../../types/businessStack';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { useDebounce } from '@/hooks/useDebounce';

export const AvailableAppsGrid: React.FC<AvailableAppsGridProps> = ({
  apps,
  isLoading,
  searchQuery,
  onSearchChange,
  onConnectApp,
  onOpenOutlookOrganisationModal,
  onOpenOutlookPersonalModal,
  onOpenSharePointModal,
  onOpenSalesforceModal,
  onOpenTwilioModal,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  const debouncedSearchQuery = useDebounce(localSearchQuery, 1000);

  // Sync local state when parent searchQuery changes (e.g., when parent resets search)
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Trigger API call when debounced value changes
  useEffect(() => {
    // Only call onSearchChange if the debounced value is different from current searchQuery
    if (debouncedSearchQuery !== searchQuery) {
      onSearchChange(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, onSearchChange, searchQuery]);

  // Handle input change - update local state immediately for responsive UI
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  // Filter apps based on search query
  const filteredApps = apps.filter(app =>
    app.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="flex h-full flex-col">
      {/* Search Bar */}
      <div className="mb-6 flex-shrink-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            value={localSearchQuery}
            onChange={handleSearchInputChange}
            className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>
      </div>

      {/* Apps Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex h-full items-center justify-center">
            <div className="flex flex-col items-center gap-3">
              <Spinner />
              <p className="text-sm text-gray-500">Loading available apps...</p>
            </div>
          </div>
        ) : filteredApps.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <div className="mb-4 text-4xl">🔍</div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                {searchQuery ? 'No apps found' : 'No apps available'}
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? `No apps match "${searchQuery}". Try a different search term.`
                  : 'There are no available apps to display at the moment.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-x-4 gap-y-10 sm:grid-cols-2 lg:grid-cols-4">
            {filteredApps.map((app, index) => (
              <AppCard
                key={`${app.name}-${index}`}
                app={app}
                onConnect={onConnectApp}
                onOpenOutlookOrganisationModal={onOpenOutlookOrganisationModal}
                onOpenOutlookPersonalModal={onOpenOutlookPersonalModal}
                onOpenSharePointModal={onOpenSharePointModal}
                onOpenSalesforceModal={onOpenSalesforceModal}
                onOpenTwilioModal={onOpenTwilioModal}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
