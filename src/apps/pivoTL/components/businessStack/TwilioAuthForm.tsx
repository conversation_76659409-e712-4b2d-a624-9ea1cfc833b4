import React, { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { TwilioAuthFormProps } from '../../types/businessStack';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

export const TwilioAuthForm: React.FC<TwilioAuthFormProps> = ({
  onSubmit,
  isLoading,
  error,
}) => {
  const [accountSid, setAccountSid] = useState('');
  const [authToken, setAuthToken] = useState('');
  const [showAccountSid, setShowAccountSid] = useState(false);
  const [showAuthToken, setShowAuthToken] = useState(false);
  const [validationErrors, setValidationErrors] = useState({
    accountSid: '',
    authToken: '',
  });

  // Validate Account SID format
  const validateAccountSid = (value: string): boolean => {
    if (!value.trim()) {
      setValidationErrors(prev => ({
        ...prev,
        accountSid: 'Account SID is required.',
      }));
      return false;
    }

    // Twilio Account SID format: starts with 'AC' followed by 32 hex characters
    const accountSidRegex = /^AC[a-fA-F0-9]{32}$/;
    if (!accountSidRegex.test(value)) {
      setValidationErrors(prev => ({
        ...prev,
        accountSid:
          'Please enter a valid Account SID (starts with AC followed by 32 characters).',
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, accountSid: '' }));
    return true;
  };

  // Validate Auth Token format
  const validateAuthToken = (value: string): boolean => {
    if (!value.trim()) {
      setValidationErrors(prev => ({
        ...prev,
        authToken: 'Auth Token is required.',
      }));
      return false;
    }

    // Twilio Auth Token is typically 32 hex characters
    const authTokenRegex = /^[a-fA-F0-9]{32}$/;
    if (!authTokenRegex.test(value)) {
      setValidationErrors(prev => ({
        ...prev,
        authToken: 'Please enter a valid Auth Token (32 characters).',
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, authToken: '' }));
    return true;
  };

  const handleAccountSidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountSid(value);

    // Clear validation error when user starts typing
    if (validationErrors.accountSid) {
      setValidationErrors(prev => ({ ...prev, accountSid: '' }));
    }
  };

  const handleAuthTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAuthToken(value);

    // Clear validation error when user starts typing
    if (validationErrors.authToken) {
      setValidationErrors(prev => ({ ...prev, authToken: '' }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const isAccountSidValid = validateAccountSid(accountSid);
    const isAuthTokenValid = validateAuthToken(authToken);

    if (isAccountSidValid && isAuthTokenValid) {
      onSubmit(accountSid.trim(), authToken.trim());
    }
  };

  const isFormValid =
    accountSid.trim() &&
    authToken.trim() &&
    !validationErrors.accountSid &&
    !validationErrors.authToken;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label
          htmlFor="accountSid"
          className="block text-sm font-semibold text-gray-700"
        >
          Account SID{' '}
          <span className="font-normal text-red-500">(Required)</span>
        </label>
        <p className="my-2 text-sm text-gray-600">
          Log into your Twilio account and find "API Credentials" on this page{' '}
          <a
            className="text-violet-500 underline"
            href="https://www.twilio.com/user/account/settings"
          >
            https://www.twilio.com/user/account/settings
          </a>
        </p>
        <div className="relative mt-1">
          <input
            type={showAccountSid ? 'text' : 'password'}
            id="accountSid"
            value={accountSid}
            onChange={handleAccountSidChange}
            disabled={isLoading}
            placeholder="e.g., ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            className={`w-full rounded-md border px-3 py-2 pr-10 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:bg-gray-50 ${
              validationErrors.accountSid || error
                ? 'border-red-300 focus:border-red-500'
                : 'border-gray-300 focus:border-primary'
            }`}
          />
          <button
            type="button"
            onClick={() => setShowAccountSid(!showAccountSid)}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            {showAccountSid ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
        {validationErrors.accountSid && (
          <p className="mt-1 text-xs text-red-600">
            {validationErrors.accountSid}
          </p>
        )}
      </div>

      <div>
        <label
          htmlFor="authToken"
          className="block text-sm font-semibold text-gray-700"
        >
          Auth Token{' '}
          <span className="font-normal text-red-500">(Required)</span>
        </label>
        <p className="my-2 text-sm text-gray-600">
          Found directly below your Account SID.
        </p>
        <div className="relative mt-1">
          <input
            type={showAuthToken ? 'text' : 'password'}
            id="authToken"
            value={authToken}
            onChange={handleAuthTokenChange}
            disabled={isLoading}
            placeholder="Enter your Twilio Auth Token"
            className={`w-full rounded-md border px-3 py-2 pr-10 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:bg-gray-50 ${
              validationErrors.authToken || error
                ? 'border-red-300 focus:border-red-500'
                : 'border-gray-300 focus:border-primary'
            }`}
          />
          <button
            type="button"
            onClick={() => setShowAuthToken(!showAuthToken)}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            {showAuthToken ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
        {validationErrors.authToken && (
          <p className="mt-1 text-xs text-red-600">
            {validationErrors.authToken}
          </p>
        )}
      </div>

      {error && <p className="text-xs text-red-600">{error}</p>}

      <div className="flex justify-center space-x-3">
        <button
          type="submit"
          disabled={!isFormValid || isLoading}
          className={`flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors ${
            isFormValid && !isLoading
              ? 'bg-primary text-white hover:bg-primary/90'
              : 'cursor-not-allowed bg-gray-300 text-gray-500'
          }`}
        >
          {isLoading ? (
            <>
              <Spinner />
              <span className="ml-2">Connecting...</span>
            </>
          ) : (
            'Continue To Twilio'
          )}
        </button>
      </div>
    </form>
  );
};
