import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';
import {
  SalesforceConnectionModalProps,
  ConnectionFlowState,
} from '../../types/businessStack';
import { DomainIdForm } from '../businessStack/DomainIdForm';
import { useSalesforceConnectionApi } from '../../services/businessStackService';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { connectedIcon, redirectIcon } from '../../assets/icons';

export const SalesforceConnectionModal: React.FC<
  SalesforceConnectionModalProps
> = ({ isOpen, onClose, onConnectionSuccess }) => {
  const [flowState, setFlowState] = useState<ConnectionFlowState>({
    step: 'tenant-input', // Reusing the same flow state structure
    isLoading: false,
  });

  const { getConsentUrl, exchangeAccessToken } = useSalesforceConnectionApi();

  // Extract state parameter from URL
  const extractStateFromUrl = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('state');
    } catch {
      return null;
    }
  };

  // Handle OAuth popup window
  const handleOAuthPopup = (
    consentUrl: string,
  ): Promise<{ state: string; code?: string }> => {
    return new Promise((resolve, reject) => {
      const popup = window.open(
        consentUrl,
        'salesforce-oauth',
        'width=600,height=700,scrollbars=yes,resizable=yes',
      );

      if (!popup) {
        reject(new Error('Popup blocked. Please allow popups and try again.'));
        return;
      }

      // Monitor popup for completion
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new Error('Authentication was cancelled.'));
        }
      }, 1000);

      // Listen for messages from popup (if redirect_uri posts back)
      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'SALESFORCE_OAUTH_SUCCESS') {
          clearInterval(checkClosed);
          popup.close();
          window.removeEventListener('message', messageListener);
          resolve({
            state: event.data.state,
            code: event.data.code,
          });
        } else if (event.data.type === 'SALESFORCE_OAUTH_ERROR') {
          clearInterval(checkClosed);
          popup.close();
          window.removeEventListener('message', messageListener);
          reject(new Error(event.data.error || 'Authentication failed.'));
        }
      };

      window.addEventListener('message', messageListener);

      // Fallback: Check popup URL periodically (if redirect_uri is same domain)
      const urlCheckInterval = setInterval(() => {
        try {
          if (popup.location && popup.location.href) {
            const state = extractStateFromUrl(popup.location.href);
            if (state) {
              clearInterval(checkClosed);
              clearInterval(urlCheckInterval);
              popup.close();
              window.removeEventListener('message', messageListener);
              resolve({
                state,
                code:
                  new URLSearchParams(popup.location.search).get('code') ||
                  undefined,
              });
            }
          }
        } catch (e) {
          // Cross-origin error is expected during OAuth flow
        }
      }, 1000);
    });
  };

  // Handle domain ID submission
  const handleDomainIdSubmit = async (domainId: string) => {
    try {
      setFlowState(prev => ({
        ...prev,
        isLoading: true,
        error: undefined,
        tenantId: domainId, // Reusing tenantId field for domain
      }));

      // Step 1: Get consent URL
      const consentResponse = await getConsentUrl(domainId);
      const consentUrl = consentResponse.data.data;
      const state = extractStateFromUrl(consentUrl);

      setFlowState(prev => ({
        ...prev,
        step: 'authenticating',
        consentUrl,
        state: state || undefined,
      }));

      // Step 2: Handle OAuth authentication
      const authResult = await handleOAuthPopup(consentUrl);

      setFlowState(prev => ({
        ...prev,
        step: 'exchanging-token',
        state: authResult.state,
      }));

      // Step 3: Exchange for access token
      const tokenResponse = await exchangeAccessToken({
        state: authResult.state,
        code: authResult.code || '',
      });

      if (tokenResponse.status) {
        setFlowState(prev => ({ ...prev, step: 'success', isLoading: false }));

        // Auto-close modal and trigger success callback after showing success message
        setTimeout(() => {
          onConnectionSuccess();
          onClose();
        }, 2000);
      } else {
        throw new Error(
          tokenResponse.message || 'Failed to connect to Salesforce.',
        );
      }
    } catch (error) {
      console.error('Salesforce connection flow error:', error);
      setFlowState(prev => ({
        ...prev,
        step: 'error',
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
      }));
    }
  };

  // Reset modal state when closed
  const handleClose = () => {
    setFlowState({
      step: 'tenant-input',
      isLoading: false,
    });
    onClose();
  };

  // Render step content
  const renderStepContent = () => {
    switch (flowState.step) {
      case 'tenant-input':
        return (
          <DomainIdForm
            onSubmit={handleDomainIdSubmit}
            isLoading={flowState.isLoading}
            error={flowState.error}
          />
        );

      case 'authenticating':
        return (
          <div className="text-center">
            <Spinner />
            <h3 className="mt-4 font-medium text-gray-900">
              Connecting to Salesforce
            </h3>
            <p className="mt-2 text-sm text-gray-600">
              {flowState.isLoading
                ? 'Setting up your connection...'
                : 'Please complete the authentication in the popup window.'}
            </p>
          </div>
        );

      case 'exchanging-token':
        return (
          <div className="text-center">
            <Spinner />
            <h3 className="mt-4 font-medium text-gray-900">
              Finalizing Connection
            </h3>
            <p className="mt-2 text-sm text-gray-600">
              Setting up your Salesforce connection...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="flex items-center gap-4">
            <img
              src={connectedIcon}
              alt="✓"
              className="h-14 w-14 flex-shrink-0 rounded-lg bg-success"
            />
            <div>
              <h3 className="mt-4 text-lg font-semibold text-blackOne">
                Success! Salesforce is now connected to PivoTL.
              </h3>
              <p className="mt-2 text-sm text-darkGray">
                You’re all set. We’ll begin syncing the approved data and
                features shortly.
              </p>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="flex flex-col items-center text-center">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-red-600">
                Connection Failed
              </h3>
              <p className="text-sm text-gray-600">
                {flowState.error || 'An unexpected error occurred.'}
              </p>
            </div>

            <button
              onClick={() =>
                setFlowState({ step: 'tenant-input', isLoading: false })
              }
              className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              Try Again
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm"
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="relative mx-4 w-full max-w-md rounded-lg bg-white p-6 shadow-xl"
            onClick={e => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={handleClose}
              className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>

            <div className="flex h-full flex-col items-center justify-center py-4">
              <div className="flex items-center justify-center pb-6 pt-2">
                <img
                  src={redirectIcon}
                  alt="✓"
                  className="h-14 w-14 flex-shrink-0 rounded-lg bg-yellowOne"
                />
              </div>
              {/* Main text */}
              <div className="mb-6 text-center">
                <p className="mt-1 text-lg font-semibold leading-[150%] text-blackOne">
                  You will be redirected to securely connect your Salesforce
                  account.
                </p>
              </div>

              {/* Step Content */}
              <div className="w-full">{renderStepContent()}</div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
