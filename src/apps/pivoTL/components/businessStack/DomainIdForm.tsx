import React, { useState } from 'react';
import { DomainIdFormProps } from '../../types/businessStack';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

export const DomainIdForm: React.FC<DomainIdFormProps> = ({
  onSubmit,
  isLoading,
  error,
}) => {
  const [domainId, setDomainId] = useState('');
  const [validationError, setValidationError] = useState('');

  // Validate domain ID format (basic validation)
  const validateDomainId = (value: string): boolean => {
    if (!value.trim()) {
      setValidationError('Domain ID is required.');
      return false;
    }

    // Basic format validation - domain ID should be a valid domain format
    // Salesforce domains typically follow patterns like: company.my.salesforce.com or company.lightning.force.com
    const salesforceDomainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.(my\.salesforce\.com|lightning\.force\.com|salesforce\.com)$/;
    const customDomainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;

    if (!salesforceDomainRegex.test(value) && !customDomainRegex.test(value)) {
      setValidationError(
        'Please enter a valid Salesforce domain (e.g., company.my.salesforce.com or company.lightning.force.com).',
      );
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDomainId(value);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateDomainId(domainId)) {
      onSubmit(domainId.trim());
    }
  };

  const isFormValid = domainId.trim() && !validationError;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label
          htmlFor="domainId"
          className="block text-sm font-medium text-gray-700"
        >
          Salesforce Domain
        </label>
        <div className="mt-1">
          <input
            type="text"
            id="domainId"
            value={domainId}
            onChange={handleInputChange}
            disabled={isLoading}
            placeholder="e.g., company.my.salesforce.com or company.lightning.force.com"
            className={`w-full rounded-md border px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:bg-gray-50 ${
              validationError || error
                ? 'border-red-300 focus:border-red-500'
                : 'border-gray-300 focus:border-primary'
            }`}
          />
        </div>
        {(validationError || error) && (
          <p className="mt-1 text-xs text-red-600">
            {validationError || error}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-center space-x-3">
        <button
          type="submit"
          disabled={!isFormValid || isLoading}
          className={`flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors ${
            isFormValid && !isLoading
              ? 'bg-primary text-white hover:bg-primary/90'
              : 'cursor-not-allowed bg-gray-300 text-gray-500'
          }`}
        >
          {isLoading ? (
            <>
              <Spinner />
              <span className="ml-2">Connecting...</span>
            </>
          ) : (
            'Proceed to Connect'
          )}
        </button>
      </div>
    </form>
  );
};
