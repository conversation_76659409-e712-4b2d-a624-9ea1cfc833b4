import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';
import { TwilioConnectionModalProps } from '../../types/businessStack';
import { TwilioAuthForm } from '../businessStack/TwilioAuthForm';
import { useTwilioConnectionApi } from '../../services/businessStackService';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import { connectedIcon } from '../../assets/icons';

type TwilioFlowStep = 'auth-input' | 'connecting' | 'success' | 'error';

interface TwilioFlowState {
  step: TwilioFlowStep;
  isLoading: boolean;
  error?: string;
}

export const TwilioConnectionModal: React.FC<TwilioConnectionModalProps> = ({
  isOpen,
  onClose,
  onConnectionSuccess,
}) => {
  const [flowState, setFlowState] = useState<TwilioFlowState>({
    step: 'auth-input',
    isLoading: false,
  });

  const { authorize } = useTwilioConnectionApi();

  // Handle auth form submission
  const handleAuthSubmit = async (accountSid: string, authToken: string) => {
    try {
      setFlowState(prev => ({
        ...prev,
        step: 'connecting',
        isLoading: true,
        error: undefined,
      }));

      // Call Twilio authorization API
      const response = await authorize({
        accountSid,
        authToken,
      });

      if (response.status) {
        setFlowState(prev => ({
          ...prev,
          step: 'success',
          isLoading: false,
        }));

        // Auto-close modal and trigger success callback after showing success message
        setTimeout(() => {
          onConnectionSuccess();
          onClose();
        }, 2000);
      } else {
        throw new Error(response.message || 'Failed to connect to Twilio.');
      }
    } catch (error) {
      console.error('Twilio connection flow error:', error);
      setFlowState(prev => ({
        ...prev,
        step: 'error',
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
      }));
    }
  };

  // Reset modal state when closed
  const handleClose = () => {
    setFlowState({
      step: 'auth-input',
      isLoading: false,
    });
    onClose();
  };

  // Render step content
  const renderStepContent = () => {
    switch (flowState.step) {
      case 'auth-input':
        return (
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 px-6 text-center text-lg font-semibold capitalize text-gray-900">
              Allow PivoTL to access your Twilio Account?
            </h3>
            <TwilioAuthForm
              onSubmit={handleAuthSubmit}
              isLoading={flowState.isLoading}
              error={flowState.error}
            />
          </div>
        );

      case 'connecting':
        return (
          <div className="text-center">
            <Spinner />
            <h3 className="mt-4 text-lg font-semibold text-gray-900">
              Connecting to Twilio
            </h3>
            <p className="mt-2 text-sm text-gray-600">
              Verifying your credentials and setting up the connection...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="flex items-center gap-4">
            <img
              src={connectedIcon}
              alt="✓"
              className="h-14 w-14 flex-shrink-0 rounded-lg bg-success"
            />
            <div>
              <h3 className="mt-4 text-lg font-semibold text-blackOne">
                Success! Twilio is now connected to PivoTL.
              </h3>
              <p className="mt-2 text-sm text-darkGray">
                You’re all set. We’ll begin syncing the approved data and
                features shortly.
              </p>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <X className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="mb-2 text-lg font-semibold text-red-600">
              Connection Failed
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              {flowState.error || 'An unexpected error occurred.'}
            </p>
            <button
              onClick={() =>
                setFlowState({ step: 'auth-input', isLoading: false })
              }
              className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              Try Again
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="w-full max-w-[585px] rounded-lg bg-white p-6 pb-12 shadow-xl"
            onClick={e => e.stopPropagation()}
          >
            <div className="mb-4 flex items-center justify-end">
              <button
                onClick={handleClose}
                className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="md:px-16">{renderStepContent()}</div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
