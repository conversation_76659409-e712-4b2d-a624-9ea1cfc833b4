import React, { useState } from 'react';
import { TenantIdFormProps } from '../../types/businessStack';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

export const TenantIdForm: React.FC<TenantIdFormProps> = ({
  onSubmit,
  isLoading,
  error,
}) => {
  const [tenantId, setTenantId] = useState('');
  const [validationError, setValidationError] = useState('');

  // Validate tenant ID format (basic validation)
  const validateTenantId = (value: string): boolean => {
    if (!value.trim()) {
      setValidationError('Tenant ID is required.');
      return false;
    }

    // Basic format validation - tenant ID should be a GUID or domain
    const guidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const domainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.onmicrosoft\.com$/;
    const customDomainRegex =
      /^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;

    if (
      !guidRegex.test(value) &&
      !domainRegex.test(value) &&
      !customDomainRegex.test(value)
    ) {
      setValidationError(
        'Please enter a valid tenant ID (GUID format) or domain (e.g., contoso.onmicrosoft.com).',
      );
      return false;
    }

    setValidationError('');
    return true;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTenantId(value);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateTenantId(tenantId)) {
      onSubmit(tenantId.trim());
    }
  };

  const isFormValid = tenantId.trim() && !validationError;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label
          htmlFor="tenantId"
          className="block text-sm font-medium text-gray-700"
        >
          Microsoft Tenant ID
        </label>
        <div className="mt-1">
          <input
            type="text"
            id="tenantId"
            value={tenantId}
            onChange={handleInputChange}
            disabled={isLoading}
            placeholder="e.g., contoso.onmicrosoft.com or 12345678-1234-1234-1234-123456789012"
            className={`w-full rounded-md border px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:bg-gray-50 ${
              validationError || error
                ? 'border-red-300 focus:border-red-500'
                : 'border-gray-300 focus:border-primary'
            }`}
          />
        </div>

        {/* Validation Error */}
        {validationError && (
          <p className="mt-1 text-sm text-red-600">{validationError}</p>
        )}

        {/* API Error */}
        {error && !validationError && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-center space-x-3">
        <button
          type="submit"
          disabled={!isFormValid || isLoading}
          className={`flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors ${
            isFormValid && !isLoading
              ? 'bg-primary text-white hover:bg-primary/90'
              : 'cursor-not-allowed bg-gray-300 text-gray-500'
          }`}
        >
          {isLoading ? (
            <>
              <Spinner />
              <span className="ml-2">Connecting...</span>
            </>
          ) : (
            'Proceed to Connect'
          )}
        </button>
      </div>
    </form>
  );
};
