import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LogOut } from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { usePivotlAuth } from '../../context/PivotlAuthContext';
import { ROUTES } from '../../constants/routes';
import { Icons } from '../../assets/icons/DashboardIcons';

interface UserDropdownProps {
  className?: string;
}

export const UserDropdown: React.FC<UserDropdownProps> = ({
  className = '',
}) => {
  const pathname = useLocation().pathname;
  const [isOpen, setIsOpen] = useState(false);
  const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const { user, getUserInitials, logout } = usePivotlAuth();
  const navigate = useNavigate();

  const userInitials = getUserInitials();
  const userName = user
    ? `${user.firstName || ''} ${user.lastName || ''}`.trim()
    : 'User';
  const userEmail = user?.email || '';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleProfileClick = () => {
    setIsOpen(false);
    navigate(ROUTES.DASHBOARD_SETTINGS);
  };

  const handleLogout = () => {
    setIsOpen(false);
    logout();
  };

  const toggleDropdown = () => {
    if (!isOpen && buttonRef.current) {
      setButtonRect(buttonRef.current.getBoundingClientRect());
    }
    setIsOpen(!isOpen);
  };

  return (
    <>
      <div className={`relative ${className}`}>
        {/* Avatar Button */}
        <div
          ref={buttonRef}
          className="flex h-10 w-10 cursor-pointer items-center justify-center overflow-hidden rounded-full border-2 border-primary bg-gray-900 transition-colors hover:bg-gray-800"
          onClick={toggleDropdown}
        >
          {user?.profilePicture ? (
            <img
              src={user.profilePicture}
              alt={userName}
              className="h-full w-full rounded-full object-cover"
            />
          ) : (
            <span className="mt-0.5 text-sm font-medium text-white">
              {userInitials}
            </span>
          )}
        </div>
      </div>

      {/* Dropdown Menu - Rendered via Portal */}
      {isOpen &&
        buttonRect &&
        createPortal(
          <AnimatePresence>
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.15, ease: 'easeOut' }}
              className="fixed z-[9999] w-52 rounded-xl border border-gray-200 bg-white shadow-[0_0_10px_rgba(0,0,0,0.1)]"
              style={{
                top: buttonRect.bottom + 8,
                right: window.innerWidth - buttonRect.right,
              }}
            >
              {/* User Info Section */}
              <div className="border-b border-gray-100 px-2 py-3">
                <div
                  onClick={handleProfileClick}
                  className="flex cursor-pointer items-center space-x-3 rounded-xl p-1 transition-all duration-300 hover:bg-gray-50 hover:p-2"
                >
                  <div className="flex h-8 w-8 flex-col items-center justify-center rounded-full bg-primary">
                    {user?.profilePicture ? (
                      <img
                        src={user.profilePicture}
                        alt={userName}
                        className="h-full w-full rounded-full object-cover"
                      />
                    ) : (
                      <span className="mt-1 text-[10px] font-medium text-white">
                        {userInitials}
                      </span>
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-xs font-medium text-gray-900">
                      {userName}
                    </p>
                    <p className="truncate text-xs text-gray-500">
                      {userEmail}
                    </p>
                  </div>
                </div>
              </div>

              {/* Menu Items */}
              <div className="py-1">
                {!pathname.includes(ROUTES.DASHBOARD_BASE) && (
                  <Link
                    to={ROUTES.DASHBOARD_BASE}
                    onClick={handleProfileClick}
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50"
                  >
                    <Icons.User className="mr-3 h-4 w-4" />
                    Dashboard
                  </Link>
                )}

                {!pathname.includes(ROUTES.DASHBOARD_BASE) && (
                  <div className="my-1 border-t border-gray-100" />
                )}

                <button
                  onClick={handleLogout}
                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50"
                >
                  <LogOut className="mr-3 h-4 w-4" />
                  Log out
                </button>
              </div>
            </motion.div>
          </AnimatePresence>,
          document.body,
        )}
    </>
  );
};
