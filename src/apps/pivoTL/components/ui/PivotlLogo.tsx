import { Link } from 'react-router-dom';
import { pivotlLogo } from '../../assets/icons';
import { pivotlRoundedLogo } from '../../assets/images';
import { ROUTES } from '../../constants/routes';

interface PivotlLogoProps {
  variant?: 'dark' | 'light';
  rounded?: boolean;
}

const PivotlLogo = ({ variant = 'dark', rounded = false }: PivotlLogoProps) => {
  const textColor = variant === 'dark' ? 'text-gray-900' : 'text-white';

  return (
    <Link to={ROUTES.PIVOTL_HOME}>
      <div className="flex items-center space-x-2">
        <img loading="lazy" src={rounded ? pivotlRoundedLogo : pivotlLogo} />
        <span className={`font-space-mono text-3xl font-bold ${textColor}`}>
          PivoTL
        </span>
      </div>
    </Link>
  );
};

export default PivotlLogo;
