import { Outlet } from 'react-router-dom';
import { PivotlFooter } from './PivotlFooter';
import { PivotlErrorBoundary } from '../ui/PivotlErrorBoundary';

export const PivotlOnboardingShell = () => {
  return (
    <PivotlErrorBoundary>
      <div className="flex min-h-screen flex-col">
        <main className="flex-1">
          <Outlet />
        </main>
        <PivotlFooter />
      </div>
    </PivotlErrorBoundary>
  );
};
