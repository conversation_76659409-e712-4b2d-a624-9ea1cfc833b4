'use client';

import Button from '@/components/ui/ButtonComponent';
import { motion } from 'framer-motion';
import { Search, X } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import PivotlLogo from '../ui/PivotlLogo';
import { UserDropdown } from '../ui/UserDropdown';
import { ROUTES } from '../../constants/routes';

interface DashboardHeaderProps {
  isSidebarCollapsed: boolean;
}

// eslint-disable-next-line no-empty-pattern
export function DashboardHeader({}: DashboardHeaderProps) {
  const location = useLocation();
  const navigate = useNavigate();

  const isSettingsPage = location.pathname.includes('/settings');

  const handleCloseSettings = () => {
    navigate(ROUTES.DASHBOARD_AI_AGENTS);
  };

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="border-b border-[#E6E6E6] bg-[#FFFDF9]/80 px-4 py-3 backdrop-blur-sm"
    >
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex flex-shrink-0 items-center gap-4 md:w-[300px]">
          <PivotlLogo variant="dark" rounded />
        </div>

        {/* Right Section */}
        <div className="flex w-full items-center justify-between gap-4">
          {isSettingsPage ? (
            <h1 className="text-2xl font-medium text-subText">Settings</h1>
          ) : (
            <span />
          )}
          <div className="flex items-center gap-4">
            {isSettingsPage ? (
              // Settings mode - show close button
              <button
                onClick={handleCloseSettings}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-primary transition-colors hover:bg-red-600 md:h-[48px] md:w-[48px]"
                title="Close settings"
              >
                <X className="h-4 w-4 text-white" />
              </button>
            ) : (
              // Normal mode - show regular buttons and user dropdown
              <>
                {/* Mobile Search Button */}
                <button className="flex h-[44px] items-center justify-center rounded-lg p-4 hover:bg-gray-100">
                  <Search className="h-5 w-5" />
                </button>

                <Button className="h-[44px] w-[143px] rounded-lg bg-primary text-white">
                  Chat with Pivo
                </Button>

                <Button className="h-[44px] rounded-lg border border-primary bg-white p-4 text-primary">
                  Upgrade
                </Button>
                <UserDropdown />
              </>
            )}
          </div>
        </div>
      </div>
    </motion.header>
  );
}
