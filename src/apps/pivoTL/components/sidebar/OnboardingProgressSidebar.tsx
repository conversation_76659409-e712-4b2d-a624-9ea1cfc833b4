import { useState, FormEvent } from 'react';
import { Eye, EyeOff, Check, X, ArrowUpFromDot } from 'lucide-react';
import { OnboardingProgress, PasswordValidation } from '../../types/chat';
import { halfEllipse } from '../../assets/images';
import { VerificationCodeInput } from '../chat/VerificationCodeInput';

interface OnboardingProgressSidebarProps {
  onboardingProgress: OnboardingProgress;
  onResendVerificationCode: () => void;
  onSubmitVerificationCode: (code: string) => Promise<void>;
  onSubmitPassword: (password: string) => Promise<void>;
}

export const OnboardingProgressSidebar = ({
  onboardingProgress,
  onResendVerificationCode,
  onSubmitVerificationCode,
  onSubmitPassword,
}: OnboardingProgressSidebarProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState(onboardingProgress.password || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validatePassword = (password: string): PasswordValidation => {
    return {
      minLength: password.length >= 8,
      hasLowercase: /[a-z]/.test(password),
      hasUppercase: /[A-Z]/.test(password),
      hasDigit: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
  };

  const passwordValidation = validatePassword(password);
  const isPasswordValid = Object.values(passwordValidation).every(Boolean);

  const ValidationIcon = ({ isValid }: { isValid: boolean }) =>
    isValid ? (
      <Check className="h-4 w-4 text-successTwo" />
    ) : (
      <X className="h-4 w-4 text-red-600" />
    );

  const handleVerificationSubmit = (code: string) => {
    if (code.length === 6 && /^\d{6}$/.test(code)) {
      onSubmitVerificationCode(code);
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (onboardingProgress.currentStep === 'password' && isPasswordValid) {
      setIsSubmitting(true);
      try {
        await onSubmitPassword(password);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Show completion state if email is verified
  if (onboardingProgress.isSignupCompleted) {
    return (
      <div className="sticky top-8 flex h-full flex-col justify-center rounded-lg bg-gradient-to-br from-white to-orange-50 p-6 font-inter">
        <div className="rounded-lg border border-peachTwo bg-white px-6 py-12 backdrop-blur-sm">
          <h3 className="mb-2 text-center text-lg font-semibold text-primary">
            Onboarding Completed
          </h3>
          <p className="mb-4 text-center text-sm text-blackTwo">
            Your account has been successfully created and verified.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="sticky top-8 flex h-full flex-col justify-center rounded-lg bg-gradient-to-br from-orange-50/50 to-orange-100 font-inter">
      {/* Background Grid */}
      <div
        className="absolute inset-0 z-0 -mt-40 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEllipse})`,
          backgroundSize: 'auto',
        }}
      />

      <form
        onSubmit={handleSubmit}
        className="mx-3 rounded bg-white p-6 backdrop-blur-sm"
      >
        <div className="flex justify-center">
          <h3 className="mb-6 text-lg font-medium text-darkGray">Sign Up</h3>
        </div>

        <div className="space-y-6">
          {/* First Name */}
          {onboardingProgress.firstName && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.firstName}
              </div>
            </div>
          )}

          {/* Last Name */}
          {onboardingProgress.lastName && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.lastName}
              </div>
            </div>
          )}

          {/* Email */}
          {onboardingProgress.email && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.email}
              </div>
            </div>
          )}

          {/* Verification Code */}
          {onboardingProgress.currentStep === 'verification' && (
            <div className="rounded bg-gray-100 p-4">
              <VerificationCodeInput
                email={onboardingProgress.email}
                onResend={onResendVerificationCode}
                onComplete={handleVerificationSubmit}
              />
            </div>
          )}

          {/* Password */}
          {onboardingProgress.currentStep === 'password' && (
            <div>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  className="w-full rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 pr-10 text-sm text-blackTwo focus:border-primary focus:outline-none"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-primary"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Password Validation Rules */}
              <div className="mt-2 space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.minLength} />
                  <span
                    className={
                      passwordValidation.minLength
                        ? 'text-successTwo'
                        : 'text-red-600'
                    }
                  >
                    At least 8 characters long
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasLowercase} />
                  <span
                    className={
                      passwordValidation.hasLowercase
                        ? 'text-successTwo'
                        : 'text-red-600'
                    }
                  >
                    At least one lowercase letter
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasUppercase} />
                  <span
                    className={
                      passwordValidation.hasUppercase
                        ? 'text-successTwo'
                        : 'text-red-600'
                    }
                  >
                    At least one uppercase letter
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasDigit} />
                  <span
                    className={
                      passwordValidation.hasDigit
                        ? 'text-successTwo'
                        : 'text-red-600'
                    }
                  >
                    At least one digit
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasSpecialChar} />
                  <span
                    className={
                      passwordValidation.hasSpecialChar
                        ? 'text-successTwo'
                        : 'text-red-600'
                    }
                  >
                    At least one special character
                  </span>
                </div>
              </div>

              <hr className="mt-6 border-primary" />
            </div>
          )}

          {/* Submit Button - Only shown on password step */}
          {onboardingProgress.currentStep === 'password' && (
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={!isPasswordValid || isSubmitting}
                className={`flex items-center gap-2 rounded-md border px-6 py-2 font-medium transition-colors ${
                  isPasswordValid
                    ? 'border-primary bg-lightOrangeTwo text-blue-midnight hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white'
                    : 'cursor-not-allowed bg-gray-400 text-whiteOff'
                }`}
              >
                <span>{isSubmitting ? 'Signing up...' : 'Proceed'}</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          )}
        </div>
      </form>
    </div>
  );
};
