import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon }) => (
  <div className="flex h-full flex-col items-start justify-center rounded-lg bg-white p-5">
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <div className="mr-3 flex h-[32px] w-[32px] flex-shrink-0 items-center justify-center rounded-full bg-[#FF5C0226]/15">
          {icon}
        </div>
        <div>
          <p className="mb-1 text-sm leading-[24px] text-subText">{title}</p>
          <p className="text-lg font-semibold text-[#121212]">{value}</p>
        </div>
      </div>
    </div>
  </div>
);

export default MetricCard;
