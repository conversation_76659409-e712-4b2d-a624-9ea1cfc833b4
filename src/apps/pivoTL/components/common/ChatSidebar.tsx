import { useMemo } from 'react';
import { ScyraChatInterface } from '../chat/ScyraChatInterface';
import { ChatInput } from '../chat/ChatInput';
import { eclipse, halfEllipse } from '../../assets/images';
import { useScyraChat } from '../../hooks/useScyraChat';

const ChatSidebar = () => {
  const {
    state: scyraChatState,
    sendMessage,
    groupMessagesByDate,
  } = useScyraChat();
  const groupedMessages = groupMessagesByDate();

  // Memoize the chat input component to prevent recreation on every render
  const ChatInputComponent = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder="I'm here — whenever you're ready."
        disabled={scyraChatState.isLoading}
      />
    );
  }, [sendMessage, scyraChatState.isLoading]);

  return (
    <div className="relative flex h-96 flex-col bg-gradient-to-br from-orange-50/50 to-orange-100 px-6 py-4 md:h-full md:w-1/3">
      {/* Scyra Chat Interface */}
      <div className="z-10 flex-1 overflow-hidden rounded-2xl bg-white bg-opacity-40">
        <ScyraChatInterface
          state={scyraChatState}
          ChatInputComponent={ChatInputComponent}
          groupedMessages={groupedMessages}
        />
      </div>

      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEllipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />
    </div>
  );
};

export default ChatSidebar;
