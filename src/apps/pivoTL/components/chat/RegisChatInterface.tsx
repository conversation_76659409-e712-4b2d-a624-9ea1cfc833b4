import { useEffect, useRef } from 'react';
import { RegisState } from '../../hooks/useRegisChat';
import { User } from 'lucide-react';
import { regis } from '../../assets/images';
import moment from 'moment';
import { TypingIndicator } from './TypingIndicator';

interface RegisChatInterfaceProps {
  state: RegisState;
  ChatInputComponent: React.ComponentType;
}

const RegisMessage = ({ message }: { message: any }) => {
  const isUser = message.sender === 'user';

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={regis}
              alt="Regis"
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format('h:mm A')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-3xl bg-gray-5 p-3 text-grayTwentyFour">
          {message.content}
        </div>
      </div>
    </div>
  );
};

export const RegisChatInterface = ({
  state,
  ChatInputComponent,
}: RegisChatInterfaceProps) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.messages, state.isLoading]);

  return (
    <div className="flex h-full flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
        style={{ minHeight: 0 }} // Ensures flex-1 works properly
      >
        {state.messages.map(message => (
          <RegisMessage key={message.id} message={message} />
        ))}

        {/* Typing Indicator */}
        {state.isLoading && (
          <TypingIndicator agentImageSrc={regis} agentName="Regis" />
        )}
      </div>

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">
        <ChatInputComponent />
      </div>
    </div>
  );
};
