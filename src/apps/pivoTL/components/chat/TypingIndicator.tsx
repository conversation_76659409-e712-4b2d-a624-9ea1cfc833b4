import { FC } from 'react';

interface TypingIndicatorProps {
  agentImageSrc: string;
  agentName: string;
}

export const TypingIndicator: FC<TypingIndicatorProps> = ({
  agentImageSrc,
  agentName,
}) => {
  return (
    <div className="mb-4 flex gap-3">
      {/* Agent Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        <img
          src={agentImageSrc}
          alt={agentName}
          className="h-full w-full rounded-full object-cover"
        />
      </div>

      {/* Typing Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">{agentName}</span>
        </div>

        {/* Typing Animation */}
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <span>{agentName} is typing</span>
          <div className="mt-0.5 flex gap-1">
            <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.3s]"></div>
            <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.15s]"></div>
            <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-400"></div>
          </div>
        </div>
      </div>
    </div>
  );
};
