import { cubeIcon, rhombusIcon, starIcon } from '../assets/icons';
import {
  novaIq,
  operatorIq,
  salesWing,
  scyra,
  setIq,
  liora,
  colin,
  seto,
  vesa,
  dara,
} from '../assets/images';

export const agents = [
  {
    name: 'SetIQ',
    id: 'set-iq',
    category: 'Collection Services AI Agents Suite',
    description:
      'Resolve accounts with precision, empathy, speed, and compliance.',
    image: setIq,
  },
  {
    name: 'SalesWing',
    id: 'sales-wing',
    category: 'Sales Operations AI Agents Suite',
    description: 'Engage leads, schedule calls, and accelerate deal cycles.',
    image: salesWing,
  },
  {
    name: 'OperatorIQ',
    id: 'operator-iq',
    category: 'Project & Program Oversight AI Agents Suite',
    description:
      'Track progress, manage projects, and deliver results on time.',
    image: operatorIq,
  },
  {
    name: 'NovaIQ',
    id: 'nova-iq',
    category: 'Strategy & Innovation AI Agents Suite',
    description:
      'Drive strategy, spark innovation, and align teams effectively.',
    image: novaIq,
  },
];

export const marketplaceAgents = [
  {
    name: '<PERSON><PERSON>',
    role: 'Smart Collections Communication',
    description: 'I message, resolve, and escalate when needed.',
    category: 'Collections Communication',
    image: scyra,
    features: [
      'Drives proactive, multi-channel outreach daily',
      'Reads and classifies all customer replies',
      'Learns, adapts, and improves message performance',
    ],
  },
  {
    name: 'Liora',
    role: 'Legal Document Automation',
    description: 'I draft court-ready legal documents fast.',
    category: 'Legal Document Automation',
    image: liora,
    features: [
      'Generates court-ready Summons & Complaint Packages',
      'Surfaces high-impact recovery opportunities',
      'Continuously refines prioritization with predictive signals',
    ],
  },
  {
    name: 'Colin',
    role: 'Collections Scoring',
    description: 'I score recovery likelihood and legal fit.',
    category: 'Resolution Scoring',
    image: colin,
    features: [
      'Maintains full history of all customer interactions',
      'Ensures availability across agents and campaigns',
      'Flags inconsistencies and supports investigations',
    ],
  },
  {
    name: 'Seto',
    role: 'Settlement Planning Agent',
    description: 'I recommend the right settlement terms.',
    category: 'Settlement Planning',
    image: seto,
    features: [
      'Delivers tailored settlement offers with precision',
      'Tracks agent workflow and customer outcomes',
      'Aligns every step to strategy and compliance',
    ],
  },
  {
    name: 'Vesa',
    role: 'Voice Sentiment Analysis',
    description: 'I detect tone and trigger escalation.',
    category: 'Voice Sentiment Analysis',
    image: vesa,
    features: [
      'Analyzes real-time voice tone for stress and intent',
      'Flags calls needing escalation to human agents',
      'Improves customer experience through empathy detection',
    ],
  },
  {
    name: 'Dara',
    role: 'Deposit Intelligence',
    description: 'I analyze deposits to tailor offers.',
    category: 'Deposit Intelligence',
    image: dara,
    features: [
      'Monitors deposit patterns for actionable insights',
      'Recommends personalized offers to boost retention',
      'Detects early signals of customer risk or opportunity',
    ],
  },
];

export const agentCategories = [
  'Collections Services',
  'Sales Operations',
  'Strategy & Innovation',
  'Project & Program Management',
];

export const featureIcons = [starIcon, rhombusIcon, cubeIcon];
