interface PivotlUserInfo {
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  profilePicture: string | null;
  title: string;
  email: string;
  country: string | null;
  gender: string | null;
  phoneNumberVerified: boolean;
  accountActive: boolean;
  emailVerified: boolean;
  dateOfBirth: string | Date | null;
  userRoles: string[];
  timezone: string | null;
  roleInCompany: string | null;
}

interface TenantInfo {
  id: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  tenantName: string;
  tenantAddress: string | null;
  tenantEmail: string | null;
  plan: {
    id: string;
    subscriptionType: string;
    dollarRate: string;
  };
  subscriptionStatus: string;
  trialExpiryDate: string | null;
  subscriptionExpiryDate: string | null;
}

interface PivotlUserBasicInfoPayload {
  userInfo: PivotlUserInfo;
  tenants: TenantInfo[];
}

export type { PivotlUserInfo, TenantInfo, PivotlUserBasicInfoPayload };
