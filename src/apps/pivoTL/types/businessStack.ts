export interface ScyraMessage {
  id: string;
  sender: 'user' | 'scyra';
  content: string;
  timestamp: Date;
  senderName: string;
}

export interface ScyraChatState {
  messages: ScyraMessage[];
  isLoading: boolean;
  sessionId: string;
}

export interface AvailableApp {
  name: string;
  logo: string;
  isConnected: boolean;
}

export interface AvailableAppsGridProps {
  apps: AvailableApp[];
  isLoading: boolean;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onConnectApp: (appName: string) => void;
  onOpenOutlookOrganisationModal?: (appName: string) => void;
  onOpenOutlookPersonalModal?: () => void;
  onOpenSharePointModal?: () => void;
  onOpenSalesforceModal?: () => void;
  onOpenTwilioModal?: () => void;
}

export interface ScyraChatInterfaceProps {
  state: ScyraChatState;
  ChatInputComponent: React.ComponentType;
  groupedMessages: Record<string, ScyraMessage[]>;
}

export interface AppCardProps {
  app: AvailableApp;
  onConnect: (appName: string) => void;
  onOpenOutlookOrganisationModal?: (appName: string) => void;
  onOpenOutlookPersonalModal?: () => void;
  onOpenSharePointModal?: () => void;
  onOpenSalesforceModal?: () => void;
  onOpenTwilioModal?: () => void;
}

export interface BusinessStackPageState {
  availableApps: AvailableApp[];
  isLoadingApps: boolean;
  searchQuery: string;
  scyraChatState: ScyraChatState;
  currentPage: number;
  totalPages: number;
}

export interface OutlookOrganisationConsentUrlRequest {
  tenantId: string;
}

export interface OutlookOrganisationConsentUrlResponse {
  status: boolean;
  message: string;
  data: {
    data: string;
  };
}

export interface OutlookOrganisationAccessTokenRequest {
  state: string;
}

export interface OutlookOrganisationAccessTokenResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

export interface OutlookPersonalConsentUrlResponse {
  status: boolean;
  message: string;
  data: {
    data: string;
  };
}

export interface OutlookPersonalAccessTokenRequest {
  state: string;
  code: string;
}

export interface OutlookPersonalAccessTokenResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

export interface SharePointConsentUrlRequest {
  tenantId: string;
}

export interface SharePointConsentUrlResponse {
  status: boolean;
  message: string;
  data: {
    data: string;
  };
}

export interface SharePointAccessTokenRequest {
  state: string;
  code: string;
}

export interface SharePointAccessTokenResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

export interface SalesforceConsentUrlRequest {
  domain: string;
}

export interface SalesforceConsentUrlResponse {
  status: boolean;
  message: string;
  data: {
    data: string;
  };
}

export interface SalesforceAccessTokenRequest {
  state: string;
  code: string;
}

export interface SalesforceAccessTokenResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

// Twilio Connection Types
export interface TwilioAuthorizationRequest {
  accountSid: string;
  authToken: string;
}

export interface TwilioAuthorizationResponse {
  status: boolean;
  message: string;
  data: {};
}

export interface OutlookOrganisationConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  appName: string;
  onConnectionSuccess: () => void;
}

export interface OutlookPersonalConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionSuccess: () => void;
}

export interface SharePointConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionSuccess: () => void;
}

export interface SalesforceConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionSuccess: () => void;
}

export interface TwilioConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionSuccess: () => void;
}

export interface TenantIdFormProps {
  onSubmit: (tenantId: string) => void;
  isLoading: boolean;
  error?: string;
}

export interface DomainIdFormProps {
  onSubmit: (domainId: string) => void;
  isLoading: boolean;
  error?: string;
}

export interface TwilioAuthFormProps {
  onSubmit: (accountSid: string, authToken: string) => void;
  isLoading: boolean;
  error?: string;
}

export interface OAuthPopupResult {
  success: boolean;
  state?: string;
  error?: string;
}

// Connection Flow State
export interface ConnectionFlowState {
  step:
    | 'tenant-input'
    | 'authenticating'
    | 'exchanging-token'
    | 'success'
    | 'error';
  isLoading: boolean;
  error?: string;
  tenantId?: string;
  consentUrl?: string;
  state?: string;
}
