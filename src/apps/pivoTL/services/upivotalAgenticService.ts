import { publicRequest } from '../../../lib/axios/publicRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import {
  agenticService,
  agenticUserService,
} from '../../../utils/apiServiceControllersRoute';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';

/**
 * Generates a secure session ID using Web Crypto API
 * @returns A 64-character hex string representing a secure session ID
 */
export const generateSecureSessionId = (): string => {
  // Generate 32 random bytes (256 bits) for strong security
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);

  // Convert to hex string
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export interface ChatRequest {
  userMessage: string;
  sessionId: string;
}

export interface ChatResponse {
  message: string;
}

export const useGetPivotlUserApi = () => {
  const axiosInstance = usePivotlPrivateRequest(BASE_URL, '', '');
  const getUser = async <T>(): Promise<T> => {
    const res = await axiosInstance.current?.get(
      `${agenticUserService}/accounts/me`,
      {
        timeout: 10_000_000_000,
      },
    );
    return res?.data?.data;
  };
  return getUser;
};

export const useChatWithRegisApi = () => {
  const chatWithRegis = async (payload: ChatRequest): Promise<string> => {
    try {
      const res = await publicRequest(BASE_URL)?.post(
        `${agenticService}/ai/regis/chat`,
        payload,
      );

      // API returns plain text format
      return res?.data || '';
    } catch (error) {
      console.error('Chat API Error:', error);
      throw new Error('Failed to communicate with Regis. Please try again.');
    }
  };

  return chatWithRegis;
};
