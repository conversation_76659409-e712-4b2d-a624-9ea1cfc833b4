import { useCallback } from 'react';
import { usePivotlPrivateRequest } from '../../../lib/axios/usePrivateRequest';
import { agenticService } from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';

const CHAT_ENDPOINT = `${agenticService}/ai-agents/chat`;
const HISTORY_ENDPOINT = `${CHAT_ENDPOINT}/history`;
// Replace these with data from context provider
const TENANT_ID = '48255653-fa40-4669-8606-b0013cb9d670';
const ACTIVE_AGENT = 'scyra';

export interface ScyraChatRequest {
  userMessage: string;
  sessionId: string;
}

export interface ChatHistoryItem {
  userId: string;
  conversationId: string;
  message: string;
  sender: string;
  createdAt: string;
}

type ApiResponse<T> = Promise<T>;

const useScyraApi = () => {
  return usePivotlPrivateRequest(BASE_URL, TENANT_ID, ACTIVE_AGENT);
};

// Scyra chat hook
export const useScyraChatApi = () => {
  const pivotlRequestRef = useScyraApi();

  const chatWithScyra = useCallback(
    async (payload: ScyraChatRequest): ApiResponse<string> => {
      try {
        const axiosInstance = pivotlRequestRef.current;
        if (!axiosInstance) {
          throw new Error('Axios instance not initialized');
        }

        const { data } = await axiosInstance.post(CHAT_ENDPOINT, payload);
        return data || '';
      } catch (error) {
        console.error(`${ACTIVE_AGENT} Chat API Error:`, error);
        throw new Error(
          `Failed to communicate with ${ACTIVE_AGENT}. Please try again.`,
        );
      }
    },
    [pivotlRequestRef],
  );

  return chatWithScyra;
};

// Chat history hook
export const useScyraChatHistoryApi = () => {
  const pivotlRequestRef = useScyraApi();

  const fetchChatHistory = useCallback(async (): ApiResponse<
    ChatHistoryItem[]
  > => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }

      const { data } = await axiosInstance.get(HISTORY_ENDPOINT);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      throw new Error('Failed to fetch chat history');
    }
  }, [pivotlRequestRef]);

  return fetchChatHistory;
};
