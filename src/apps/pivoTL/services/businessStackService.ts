import {
  usePivotlPrivateRequest,
  usePrivateRequest,
} from '../../../lib/axios/usePrivateRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import { integrationService } from '../../../utils/apiServiceControllersRoute';
import {
  OutlookOrganisationConsentUrlResponse,
  OutlookOrganisationAccessTokenResponse,
  OutlookPersonalConsentUrlResponse,
  OutlookPersonalAccessTokenRequest,
  OutlookPersonalAccessTokenResponse,
  SharePointConsentUrlResponse,
  SharePointAccessTokenRequest,
  SharePointAccessTokenResponse,
  SalesforceConsentUrlResponse,
  SalesforceAccessTokenRequest,
  SalesforceAccessTokenResponse,
  TwilioAuthorizationRequest,
  TwilioAuthorizationResponse,
} from '../types/businessStack';

// Replace these with data from context provider
const TENANT_ID = '48255653-fa40-4669-8606-b0013cb9d670';
const ACTIVE_AGENT = 'scyra';

// TypeScript interfaces for Business Stack API responses
interface AppDetails {
  key: string;
  name: string;
  logo: string;
  enabled: boolean;
  authType: 'OAUTH2' | 'BASIC' | string; // Add other possible auth types
  appCategory:
    | 'EMAILING_SYSTEM'
    | 'DOCUMENT_REPO'
    | 'CRM_SYSTEM'
    | 'MESSAGING_SYSTEM'
    | string;
  uiHints: {
    shortDescription: string | null;
    longDescription: string | null;
    connectButtonText: string | null;
  };
  form: {
    submitButtonText: string;
    fields: FormField[];
  } | null;
  preAuth: {
    title: string;
    submitButtonText: string;
    fields: FormField[];
  } | null;
  connectionRequestFields: FormField[];
  isConnected: boolean;
}

interface FormField {
  key: string;
  label: string | null;
  placeholder: string | null;
  description: string | null;
  type: 'TEXT' | 'PASSWORD' | string; // Add other possible field types
  required: boolean;
  minLength: number | null;
  maxLength: number | null;
  options: any[] | null; // Replace 'any' with a more specific type if options have a known structure
  order: number | null;
}

interface AvailableAppsResponse {
  status: boolean;
  message: string;
  data: {
    availableApps: AppDetails[];
    total: number;
    page: number;
    pageSize: number;
  };
}

const useApi = () => {
  return usePivotlPrivateRequest(BASE_URL, TENANT_ID, ACTIVE_AGENT);
};

// Hook for available apps API
export const useAvailableAppsApi = () => {
  const pivotlRequestRef = useApi();

  const getAvailableApps = async (params?: {
    page?: number;
    size?: number;
    search?: string;
  }): Promise<AvailableAppsResponse> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.get(
        `${integrationService}/available-apps`,
        {
          params: {
            page: params?.page || 1,
            size: params?.size || 10,
            search: params?.search || '',
          },
        },
      );
      return data || [];
    } catch (error) {
      console.error(`${ACTIVE_AGENT} Available Apps API Error:`, error);
      throw new Error(
        `Failed to fetch available apps for ${ACTIVE_AGENT}. Please try again.`,
      );
    }
  };

  return getAvailableApps;
};

// Outlook (Organisation) Connection API hooks
export const useOutlookOrganisationConnectionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getConsentUrl = async (
    tenantId: string,
  ): Promise<OutlookOrganisationConsentUrlResponse> => {
    try {
      const res = await axiosInstance.current?.get(
        `${integrationService}/microsoft-outlook-organisation/consent-url`,
        {
          params: { tenantId },
        },
      );
      return res?.data;
    } catch (error) {
      console.error('Get Consent URL API Error:', error);
      throw new Error('Failed to get consent URL. Please try again.');
    }
  };

  const exchangeAccessToken = async (
    state: string,
  ): Promise<OutlookOrganisationAccessTokenResponse> => {
    try {
      const res = await axiosInstance.current?.post(
        `${integrationService}/microsoft-outlook-organisation/access-token`,
        { state },
      );
      return res?.data;
    } catch (error) {
      console.error('Exchange Access Token API Error:', error);
      throw new Error('Failed to exchange access token. Please try again.');
    }
  };

  return {
    getConsentUrl,
    exchangeAccessToken,
  };
};

// Outlook (Personal) Connection API hooks
export const useOutlookPersonalConnectionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getConsentUrl =
    async (): Promise<OutlookPersonalConsentUrlResponse> => {
      try {
        const res = await axiosInstance.current?.get(
          `${integrationService}/microsoft-outlook-personal/consent-url`,
        );
        return res?.data;
      } catch (error) {
        console.error('Get Personal Outlook Consent URL API Error:', error);
        throw new Error('Failed to get consent URL. Please try again.');
      }
    };

  const exchangeAccessToken = async (
    payload: OutlookPersonalAccessTokenRequest,
  ): Promise<OutlookPersonalAccessTokenResponse> => {
    try {
      const res = await axiosInstance.current?.post(
        `${integrationService}/microsoft-outlook-personal/access-token`,
        payload,
      );
      return res?.data;
    } catch (error) {
      console.error('Exchange Personal Outlook Access Token API Error:', error);
      throw new Error('Failed to exchange access token. Please try again.');
    }
  };

  return {
    getConsentUrl,
    exchangeAccessToken,
  };
};

// SharePoint Connection API hooks
export const useSharePointConnectionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getConsentUrl = async (
    tenantId: string,
  ): Promise<SharePointConsentUrlResponse> => {
    try {
      const res = await axiosInstance.current?.get(
        `${integrationService}/microsoft-sharepoint/admin-consent-url`,
        {
          params: { tenantId },
        },
      );
      return res?.data;
    } catch (error) {
      console.error('Get SharePoint Consent URL API Error:', error);
      throw new Error('Failed to get consent URL. Please try again.');
    }
  };

  const exchangeAccessToken = async (
    payload: SharePointAccessTokenRequest,
  ): Promise<SharePointAccessTokenResponse> => {
    try {
      const res = await axiosInstance.current?.post(
        `${integrationService}/microsoft-sharepoint/admin-access-token`,
        payload,
      );
      return res?.data;
    } catch (error) {
      console.error('Exchange SharePoint Access Token API Error:', error);
      throw new Error('Failed to exchange access token. Please try again.');
    }
  };

  return {
    getConsentUrl,
    exchangeAccessToken,
  };
};

// Salesforce Connection API hooks
export const useSalesforceConnectionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getConsentUrl = async (
    domain: string,
  ): Promise<SalesforceConsentUrlResponse> => {
    try {
      const res = await axiosInstance.current?.get(
        `${integrationService}/salesforce/user-consent-url`,
        {
          params: { domain },
        },
      );
      return res?.data;
    } catch (error) {
      console.error('Get Salesforce Consent URL API Error:', error);
      throw new Error('Failed to get consent URL. Please try again.');
    }
  };

  const exchangeAccessToken = async (
    payload: SalesforceAccessTokenRequest,
  ): Promise<SalesforceAccessTokenResponse> => {
    try {
      const res = await axiosInstance.current?.post(
        `${integrationService}/salesforce/user-access-token`,
        payload,
      );
      return res?.data;
    } catch (error) {
      console.error('Exchange Salesforce Access Token API Error:', error);
      throw new Error('Failed to exchange access token. Please try again.');
    }
  };

  return {
    getConsentUrl,
    exchangeAccessToken,
  };
};

// Twilio Connection API hooks
export const useTwilioConnectionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const authorize = async (
    payload: TwilioAuthorizationRequest,
  ): Promise<TwilioAuthorizationResponse> => {
    try {
      const res = await axiosInstance.current?.post(
        `${integrationService}/twilio/authorization`,
        payload,
      );
      return res?.data;
    } catch (error) {
      console.error('Twilio Authorization API Error:', error);
      throw new Error(
        'Failed to authorize Twilio connection. Please try again.',
      );
    }
  };

  return {
    authorize,
  };
};

/**
 * Generates a secure session ID using Web Crypto API
 * @returns A 64-character hex string representing a secure session ID
 */
export const generateSecureSessionId = (): string => {
  // Generate 32 random bytes (256 bits) for strong security
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);

  // Convert to hex string
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};
