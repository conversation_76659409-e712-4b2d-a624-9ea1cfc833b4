import { useState, useCallback, useEffect } from 'react';
import { generateSecureSessionId } from '../services/businessStackService';
import {
  useScyraChatHistoryApi,
  ChatHistoryItem,
  useScyraChatApi,
} from '../services/scyraChatService';
import { ScyraChatState, ScyraMessage } from '../types/businessStack';
import { useConversationState } from './useConversationState';
import { MessageSender } from '../types/chat';

export const useScyraChat = () => {
  const [state, setState] = useState<ScyraChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  const chatWithScyra = useScyraChatApi();
  const fetchChatHistory = useScyraChatHistoryApi();

  // Helper to map API history to ScyraMessage
  const mapHistoryToScyraMessage = (item: ChatHistoryItem): ScyraMessage => {
    const sender = item.sender === 'user' ? 'user' : 'scyra';
    return {
      id: `${sender}-${item.createdAt}`,
      sender,
      content: item.message,
      timestamp: new Date(item.createdAt),
      senderName: sender === 'user' ? 'You' : 'Scyra',
    };
  };

  // On mount, load chat history and add welcome message if no history
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const history = await fetchChatHistory();
        let messages: ScyraMessage[] = [];
        if (history && history.length > 0) {
          messages = history
            .sort(
              (a, b) =>
                new Date(a.createdAt).getTime() -
                new Date(b.createdAt).getTime(),
            )
            .map(mapHistoryToScyraMessage);
        }
        // Always add welcome message last if no history
        if (messages.length === 0) {
          messages.push({
            id: 'welcome-message',
            sender: 'scyra',
            content: 'Hi, What would you like to do today?',
            timestamp: new Date(),
            senderName: 'Scyra',
          });
        }
        setState(prev => ({ ...prev, messages }));
      } catch (error) {
        // fallback to welcome message
        setState(prev => ({
          ...prev,
          messages: [
            {
              id: 'welcome-message',
              sender: 'scyra',
              content: 'Hi, What would you like to do today?',
              timestamp: new Date(),
              senderName: 'Scyra',
            },
          ],
        }));
      }
    };
    loadHistory();
  }, []);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || state.isLoading) return;

      // Add user message
      const userMessage: ScyraMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: 'You',
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call Scyra API
        const response = await chatWithScyra({
          userMessage: messageContent.trim(),
          sessionId: state.sessionId,
        });

        // Add Scyra response
        const scyraMessage: ScyraMessage = {
          id: `scyra-${Date.now()}`,
          sender: 'scyra',
          content: response,
          timestamp: new Date(),
          senderName: 'Scyra',
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, scyraMessage],
          isLoading: false,
        }));
      } catch (error) {
        console.error('Error sending message to Scyra:', error);

        // Add error message
        const errorMessage: ScyraMessage = {
          id: `error-${Date.now()}`,
          sender: 'scyra',
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date(),
          senderName: 'Scyra',
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithScyra, state.sessionId, state.isLoading],
  );

  // Group messages by date (YYYY-MM-DD)
  const groupMessagesByDate = () => {
    const grouped: Record<string, ScyraMessage[]> = {};
    const sorted = [...state.messages].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );
    sorted.forEach(msg => {
      const dateKey = msg.timestamp.toISOString().split('T')[0];
      if (!grouped[dateKey]) grouped[dateKey] = [];
      grouped[dateKey].push(msg);
    });
    return grouped;
  };

  return {
    state,
    sendMessage,
    groupMessagesByDate,
  };
};

export const useScyraChatHistory = () => {
  const fetchChatHistory = useScyraChatHistoryApi();
  const { addMessage } = useConversationState();

  // Converts API history item to ChatMessage format
  const mapHistoryToChatMessage = (item: ChatHistoryItem) => {
    const sender: MessageSender = item.sender === 'user' ? 'user' : 'scyra';
    return {
      id: `${sender}-${item.createdAt}`,
      sender,
      content: item.message,
      timestamp: new Date(item.createdAt),
      senderName: sender === 'user' ? 'You' : 'Scyra',
    };
  };

  // Loads and adds chat history to state, sorted by date ascending
  const loadChatHistory = useCallback(async () => {
    const history = await fetchChatHistory();
    // Sort by createdAt ascending
    const sorted = history.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );
    // Add each message to state
    sorted.forEach(item => {
      const msg = mapHistoryToChatMessage(item);
      addMessage(msg.sender, msg.content, msg.senderName);
    });
  }, [fetchChatHistory, addMessage]);

  return { loadChatHistory };
};
