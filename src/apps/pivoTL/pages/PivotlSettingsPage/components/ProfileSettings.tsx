import React, { useState } from 'react';
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { usePivotlAuth } from '../../../context/PivotlAuthContext';
import { Pencil } from 'lucide-react';
import { profilePlaceholder } from '@/assets/images';
import { Input } from '@/shared/components/ui';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import Select from 'react-select';
import { ProfileFormData } from '../../../types/profile';
import { profileSettingsSchema } from '../../../lib/yup/profileValidations';

export const ProfileSettings: React.FC = () => {
  const { user, isLoadingUserInitials, isError } = usePivotlAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const userData = user;

  // Form configuration
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSettingsSchema),
    defaultValues: {
      firstName: userData?.firstName || '',
      lastName: userData?.lastName || '',
      email: userData?.email || '',
      role: 'other', // Default role since it's not in user data yet
      timezone: 'utc-5', // Default timezone
      company: '', // Default company
    },
  });

  // Form submission handler
  const onSubmit: SubmitHandler<ProfileFormData> = async data => {
    setIsSubmitting(true);
    try {
      // Here you would typically make an API call to update the profile
      console.log('Profile data to submit:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Reset form dirty state after successful submission
      reset(data);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Options for select dropdowns based on screenshot
  const roleOptions = [
    { value: 'hr-recruiting-ops', label: 'HR & Recruiting Ops' },
    { value: 'engineering', label: 'Engineering' },
    { value: 'project-management', label: 'Project Management' },
    { value: 'marketing-ops', label: 'Marketing & Marketing Ops' },
    { value: 'business-owner', label: 'Business Owner' },
    { value: 'customer-support-ops', label: 'Customer Support Ops' },
    { value: 'data-science', label: 'Data Science' },
    { value: 'sales-ops', label: 'Sales Ops' },
    { value: 'other', label: 'Other' },
  ];

  const timezoneOptions = [
    { value: 'utc-8', label: 'UTC-8 (Pacific)' },
    { value: 'utc-5', label: 'UTC-5 (Eastern)' },
    { value: 'utc+0', label: 'UTC+0 (GMT)' },
    { value: 'utc+1', label: 'UTC+1 (Central European)' },
    { value: 'utc+8', label: 'UTC+8 (China/Singapore)' },
  ];

  // Custom styles for react-select
  const selectStyles = {
    control: (provided: any) => ({
      ...provided,
      height: '40px',
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: '1px solid #DFEAF2',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    }),
  };

  if (isLoadingUserInitials) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Spinner className="h-8 w-8" />
          <p className="text-sm text-gray-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            Failed to load profile
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Unable to load your profile information. Please try refreshing the
            page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-primary px-4 py-2 text-sm text-white hover:bg-primary/90"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-4 text-xl font-semibold text-gray-900">My Profile</h2>

        <div className="flex items-start space-x-6">
          {/* Profile Picture */}
          <div className="relative">
            <div className="flex h-20 w-20 items-center justify-center overflow-hidden rounded-full md:h-[110px] lg:w-[110px]">
              <img
                src={profilePlaceholder}
                alt="Profile"
                className="h-full w-full object-cover"
              />
            </div>
            <button className="absolute bottom-2 right-1 flex h-[25px] w-[25px] items-center justify-center rounded-full bg-primary text-xs text-white">
              <Pencil className="h-3 w-3" />
            </button>
          </div>

          {/* Profile Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="w-full max-w-2xl space-y-4"
          >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  First name
                </label>
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="text"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.firstName ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter first name"
                    />
                  )}
                />
                {errors.firstName && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Role
                </label>
                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <Select
                      options={roleOptions}
                      styles={{
                        ...selectStyles,
                        control: (provided: any) => ({
                          ...provided,
                          height: '40px',
                          border: errors.role
                            ? '1px solid #ef4444'
                            : '1px solid #DFEAF2',
                          borderRadius: '6px',
                          boxShadow: 'none',
                          '&:hover': {
                            border: errors.role
                              ? '1px solid #ef4444'
                              : '1px solid #DFEAF2',
                          },
                        }),
                      }}
                      value={roleOptions.find(
                        option => option.value === field.value,
                      )}
                      onChange={selectedOption =>
                        field.onChange(selectedOption?.value)
                      }
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      placeholder="Select role"
                      isSearchable={false}
                    />
                  )}
                />
                {errors.role && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.role.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Last name
                </label>
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="text"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.lastName ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter last name"
                    />
                  )}
                />
                {errors.lastName && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.lastName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Timezone
                </label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      options={timezoneOptions}
                      styles={{
                        ...selectStyles,
                        control: (provided: any) => ({
                          ...provided,
                          height: '40px',
                          border: errors.timezone
                            ? '1px solid #ef4444'
                            : '1px solid #DFEAF2',
                          borderRadius: '6px',
                          boxShadow: 'none',
                          '&:hover': {
                            border: errors.timezone
                              ? '1px solid #ef4444'
                              : '1px solid #DFEAF2',
                          },
                        }),
                      }}
                      value={timezoneOptions.find(
                        option => option.value === field.value,
                      )}
                      onChange={selectedOption =>
                        field.onChange(selectedOption?.value)
                      }
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      placeholder="Select timezone"
                      isSearchable={false}
                    />
                  )}
                />
                {errors.timezone && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.timezone.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Email
                </label>
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="email"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.email ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter email address"
                    />
                  )}
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 items-end gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Company
                </label>
                <Controller
                  name="company"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="text"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.company ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter company name"
                    />
                  )}
                />
                {errors.company && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.company.message}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={!isDirty || isSubmitting}
                className={`h-10 w-[130px] rounded-md px-6 py-2 text-white transition-colors ${
                  !isDirty || isSubmitting
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-grayTen hover:bg-gray-700'
                }`}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <Spinner className="h-4 w-4" />
                  </div>
                ) : (
                  'Save changes'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
