import { Link } from 'react-router-dom';
import { bgGrid } from '../../assets/images';
import { ROUTES } from '../../constants/routes';
import {
  PivotlAuthProvider,
  usePivotlAuth,
} from '../../context/PivotlAuthContext';

const HeroSectionContent = () => {
  const { isAuthenticated } = usePivotlAuth();

  return (
    <section className="relative overflow-hidden">
      {/* Background Grid */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bgGrid})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      {/* Content */}
      <div className="relative z-10 mx-auto max-w-screen-2xl px-4 py-24 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="mb-6 text-4xl font-semibold leading-normal text-gray-900 md:text-[56px]">
            The Agentic AI
            <br />
            Transformation Hub
          </h1>
          <p className="mx-auto mb-8 max-w-3xl font-inter text-lg text-gray-700">
            Orchestrate, evolve, and deploy AI agents across your enterprise.
            PivoTL is the transformation layer that powers agentic workflows,
            augments human roles, and accelerates value creation.
          </p>
          <div className="flex flex-col justify-center gap-4 px-4 sm:flex-row md:px-0">
            <Link
              to={ROUTES.PIVOTL_HOME}
              className="rounded-md bg-primary px-8 py-[9px] font-semibold text-white transition-colors hover:bg-orange-15"
            >
              Chat with Pivo
            </Link>
            {!isAuthenticated && (
              <Link
                to={ROUTES.PIVOTL_SIGNUP}
                className="rounded-md border border-gray-700 px-6 py-2 font-medium text-gray-700 transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
              >
                Register for Free
              </Link>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export const HeroSection = () => {
  return (
    <PivotlAuthProvider>
      <HeroSectionContent />
    </PivotlAuthProvider>
  );
};
