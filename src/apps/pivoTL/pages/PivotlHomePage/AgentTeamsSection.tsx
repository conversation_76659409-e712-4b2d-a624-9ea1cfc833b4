import { Link } from 'react-router-dom';
import { bgGradient } from '../../assets/images';
import { agents } from '../../data/constants';
import { ROUTES } from '../../constants/routes';

export const AgentTeamsSection = () => {
  return (
    <section className="bg-white py-20">
      {/* Gradient overlay */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bgGradient})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      <div className="container relative z-10 mx-auto -mt-20 px-6">
        <div className="text-center">
          <h2 className="mb-4 text-center text-3xl font-bold">
            Meet The AI Agent Teams
          </h2>
          <p className="mx-auto mb-12 text-center font-inter text-lg text-gray-600">
            Each PivoTL Agent team is a suite of purpose-trained AI Agents
            designed to own and resolve key enterprise workflows.
          </p>
        </div>

        {/* AI Agents */}
        <div className="flex justify-center">
          <div className="grid max-w-screen-2xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {agents.map((agent, index) => (
              <Link
                to={ROUTES.PIVOTL_AGENTS(agent.id)}
                key={index}
                className={`flex cursor-pointer flex-col overflow-hidden rounded border transition-all hover:shadow-md`}
              >
                <div className="w-[290px]">
                  <img
                    src={agent.image}
                    className="h-56 w-full object-cover"
                    alt={agent.name}
                  />
                  <div className="flex max-w-72 flex-col gap-4 p-4 text-blackOne">
                    <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                      {agent.name}
                    </div>
                    <p className="text-lg font-semibold">{agent.category}</p>
                    <p className="mb-3 max-w-60 font-inter text-darkGray">
                      {agent.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
