import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { agentCategories, marketplaceAgents } from '../../data/constants';

export const MarketplaceSection = () => {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, []);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'left' ? -300 : 300;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  return (
    <section className="bg-white pb-8 pt-12">
      <div className="container relative z-10 mx-auto max-w-screen-2xl">
        <div className="text-center">
          <h2 className="mb-4 text-center text-3xl font-bold capitalize">
            Agentic AI marketplace for Enterprise automation
          </h2>
          <p className="mx-auto mb-6 text-center font-inter text-lg text-gray-600">
            Deploy AI agents that work around the clock — scoring leads,
            resolving tasks, and scaling operations with precision.
          </p>
        </div>

        <div className="mb-6 flex flex-wrap justify-center gap-4 bg-darkGray p-4">
          {agentCategories.map((category, index) => (
            <button
              key={index}
              className="rounded-md bg-grayFifteen px-4 py-2.5 font-inter font-medium transition hover:border-blue-200 hover:bg-blue-50"
            >
              {category}
            </button>
          ))}
        </div>

        <div className="relative w-full">
          {/* Left Arrow */}
          {showLeftArrow && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}

          {/* AI Agents */}
          <div
            ref={scrollRef}
            onScroll={handleScroll}
            className="no-scrollbar flex snap-x snap-mandatory gap-6 overflow-x-auto scroll-smooth px-4 py-6"
          >
            {marketplaceAgents.map((agent, index) => (
              <div
                key={index}
                className="flex min-w-72 cursor-pointer snap-start flex-col rounded border bg-white shadow-sm transition-all hover:shadow-md"
              >
                <div className="bg-peachTwo">
                  <img
                    src={agent.image}
                    className="h-56 w-full object-contain"
                    alt={agent.name}
                  />
                </div>
                <div className="flex flex-1 flex-col gap-2 p-4">
                  <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                    {agent.name}
                  </div>
                  <p className="mt-1 text-lg font-semibold">{agent.role}</p>
                  <p className="font-inter text-darkGray">
                    {agent.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Right Arrow */}
          {showRightArrow && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
};
