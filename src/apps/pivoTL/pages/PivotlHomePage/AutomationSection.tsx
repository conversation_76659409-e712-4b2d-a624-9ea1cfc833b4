import { useEffect, useRef } from 'react';
import { digitalData } from '../../assets/videos';

export const AutomationSection = () => {
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle video playback when in viewport
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            video.play().catch(error => {
              console.log('Video play failed:', error);
            });
          } else {
            try {
              video.pause();
            } catch (error) {
              console.log('Video pause failed:', error);
            }
          }
        });
      },
      { threshold: 0.5 },
    );

    try {
      observer.observe(video);
    } catch (error) {
      console.log('IntersectionObserver failed:', error);
      return;
    }

    return () => {
      try {
        observer.unobserve(video);
      } catch (error) {
        console.log('IntersectionObserver cleanup failed:', error);
      }
    };
  }, []);

  return (
    <section className="relative h-[400px] overflow-hidden font-inter text-white">
      <div className="container relative z-10 mx-auto h-full max-w-screen-3xl">
        {/* Video Background */}
        <div className="absolute inset-0 z-0">
          <video
            ref={videoRef}
            autoPlay
            loop
            muted
            playsInline
            className="h-full w-full object-cover"
          >
            <source
              src={digitalData}
              type={`video/${digitalData.split('.').pop()}`}
            />
          </video>
          {/* Dark overlay for better text contrast */}
          <div className="absolute inset-0 bg-black/30" />
        </div>

        {/* Content Container */}
        <div className="relative z-10 mx-auto h-full max-w-screen-2xl px-4">
          <div className="flex h-full flex-col md:flex-row">
            {/* Left-aligned text on desktop, centered on mobile */}
            <div className="flex h-full w-full items-center justify-center py-8 text-center md:w-1/2 md:justify-start md:py-12 md:text-left">
              <div className="md:max-w-[450px]">
                <p className="mb-4 text-lg font-medium text-primary">
                  Beyond Automation
                </p>
                <h2 className="mb-4 text-[32px] font-bold leading-tight">
                  Empowering Agentic AI Transformation
                </h2>
                <p className="mt-2 text-lg">
                  PivoTL agents don't just complete tasks—they collaborate,
                  score, escalate, and execute workflows across your most
                  critical business functions.
                </p>
              </div>
            </div>

            {/* Right half - empty on desktop */}
            <div className="hidden md:block md:w-1/2" />
          </div>
        </div>
      </div>
    </section>
  );
};
