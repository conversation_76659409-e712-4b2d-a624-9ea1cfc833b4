import React, { useRef, useState } from 'react';
import {
  FileText,
  Users,
  MessageSquare,
  Settings,
  Shield,
  Globe,
  BookOpen,
  ChevronUp,
  ChevronDown,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import KnowledgeBaseCard from './components/KnowledgeBaseCard';
import ChatSidebar from '@/apps/pivoTL/components/common/ChatSidebar';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import { ROUTES } from '../../constants/routes';
import { motion } from 'framer-motion';
import { lioraLogo, scyraLogo } from '../../assets/images';

interface DashboardOption {
  id: string;
  name: string;
  icon: string;
}

const agentOptions: DashboardOption[] = [
  {
    id: 'scyra',
    name: 'Scyra Dashboard',
    icon: scyraLogo,
  },
  {
    id: 'seto',
    name: 'Seto Dashboard',
    icon: scyra<PERSON>ogo,
  },
  {
    id: 'liora',
    name: '<PERSON><PERSON> Dashboard',
    icon: liora<PERSON>ogo,
  },
];

const KnowledgeBasePage: React.FC = () => {
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<DashboardOption | null>(
    agentOptions[0],
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  const knowledgeBaseCategories = [
    {
      id: 'collections-agent',
      title: 'Collections Agent Operational Guide',
      description: 'How to handle leads, pipeline stages, demo scripts...',
      icon: <Users className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'communication-compliance',
      title: 'Communication Compliance Guidelines',
      description: 'How Who gets access to what and how to request...',
      icon: <Shield className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'service-engagement',
      title: 'Service Engagement Guidelines',
      description: 'How Who gets access to what and how to request...',
      icon: <MessageSquare className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'message-templates',
      title: 'Message Templates Library',
      description: 'How Who gets access to what and how to request...',
      icon: <FileText className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'communication-throttle',
      title: 'Communication Throttle Policy',
      description: 'How Who gets access to what and how to request...',
      icon: <Settings className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'settlement-escalation',
      title: 'Settlement Escalation Rules',
      description: 'How Who gets access to what and how to request...',
      icon: <BookOpen className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'escalation-contact',
      title: 'Escalation Contact Directory',
      description: 'How Who gets access to what and how to request...',
      icon: <Users className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'language-preference',
      title: 'Language Preference Profiles',
      description: 'How Who gets access to what and how to request...',
      icon: <Globe className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
    {
      id: 'agent-behavior',
      title: 'Agent Behavior Guide',
      description: 'How Who gets access to what and how to request...',
      icon: <Users className="text-primary] h-6 w-6 md:h-12 md:w-12" />,
    },
  ];

  const handleCategorySelect = (categoryId: string) => {
    navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE_CATEGORY(categoryId));
  };

  return (
    <div className="flex h-[calc(100vh-150px)] flex-col">
      <div className="flex flex-1 overflow-hidden">
        {/* Main Content - Scrollable */}
        <div className="flex flex-1 overflow-hidden">
          {/* Chat Sidebar */}
          <ChatSidebar />

          <div className="flex-1 overflow-y-auto">
            <AppContainer className="space-y-6 p-8 lg:space-y-8">
              {/* Header */}
              <div className="mb-8 flex items-center justify-between">
                <div className="flex items-center">
                  <BookOpen className="mr-3 h-5 w-5 text-primary md:h-6 md:w-6" />
                  <h1 className="text-2xl font-semibold text-[#403F3E]">
                    Knowledge Base
                  </h1>
                </div>
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex h-[48px] items-center gap-3 rounded-lg border border-[#718EBF] bg-white px-4 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 md:w-[246px]"
                  >
                    <img
                      src={selectedAgent?.icon || ''}
                      alt="Dashboard Icon"
                      className="h-6 w-6 sm:h-8 sm:w-8"
                    />
                    <span className="text-sm font-semibold text-[#121212]">
                      {selectedAgent?.name || 'Scyra Dashboard'}
                    </span>
                    {isDropdownOpen ? (
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </button>

                  {/* Dropdown Menu */}
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute left-0 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]"
                    >
                      <div className="py-1">
                        {agentOptions
                          .filter(option => option.id !== selectedAgent?.id)
                          .map(option => (
                            <button
                              key={option.id}
                              onClick={() => setSelectedAgent(option)}
                              className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50 sm:text-base"
                            >
                              <img
                                src={option.icon}
                                alt="Dashboard Icon"
                                className="h-6 w-6 sm:h-8 sm:w-8"
                              />
                              {option.name}
                            </button>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {/* Knowledge Base Grid */}
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {knowledgeBaseCategories.map(category => (
                  <KnowledgeBaseCard
                    key={category.id}
                    title={category.title}
                    description={category.description}
                    icon={category.icon}
                    onClick={() => handleCategorySelect(category.id)}
                  />
                ))}
              </div>
            </AppContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBasePage;
