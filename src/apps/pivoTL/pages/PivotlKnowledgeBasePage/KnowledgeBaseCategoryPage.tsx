import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FileText,
  Users,
  MessageSquare,
  Settings,
  Shield,
  Globe,
} from 'lucide-react';
import KnowledgeBaseTableView from './components/KnowledgeBaseTableView';
import UploadModal from './components/UploadModal';
import { ROUTES } from '../../constants/routes';

interface KnowledgeBaseItem {
  id: string;
  title: string;
  stage: string;
  targetPersona: string;
  useCase: string;
  owner: string;
  status:
    | 'Awaiting Approval'
    | 'Needs Revision'
    | 'Approved'
    | 'Missing'
    | 'Uploaded';
}

const KnowledgeBaseCategoryPage: React.FC = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  const navigate = useNavigate();
  const [showUploadModal, setShowUploadModal] = useState(false);

  const knowledgeBaseCategories = [
    {
      id: 'collections-agent',
      title: 'Collections Agent Operational Guide',
      description: 'How to handle leads, pipeline stages, demo scripts...',
      icon: <Users className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'communication-compliance',
      title: 'Communication Compliance Guidelines',
      description: 'How Who gets access to what and how to request...',
      icon: <Shield className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'service-engagement',
      title: 'Service Engagement Guidelines',
      description: 'How Who gets access to what and how to request...',
      icon: <MessageSquare className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'message-templates',
      title: 'Message Templates Library',
      description: 'How Who gets access to what and how to request...',
      icon: <FileText className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'communication-throttle',
      title: 'Communication Throttle Policy',
      description: 'How Who gets access to what and how to request...',
      icon: <Settings className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'settlement-escalation',
      title: 'Settlement Escalation Rules',
      description: 'How Who gets access to what and how to request...',
      icon: <FileText className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'escalation-contact',
      title: 'Escalation Contact Directory',
      description: 'How Who gets access to what and how to request...',
      icon: <Users className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'language-preference',
      title: 'Language Preference Profiles',
      description: 'How Who gets access to what and how to request...',
      icon: <Globe className="h-6 w-6 text-[#FF5C02]" />,
    },
    {
      id: 'agent-behavior',
      title: 'Agent Behavior Guide',
      description: 'How Who gets access to what and how to request...',
      icon: <Users className="h-6 w-6 text-[#FF5C02]" />,
    },
  ];

  const category = knowledgeBaseCategories.find(cat => cat.id === categoryId);

  const handleBackToGrid = () => {
    navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE);
  };

  const handleItemClick = (item: KnowledgeBaseItem) => {
    navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE_ITEM(categoryId!, item.id));
  };

  const handleUpload = (files: File[]) => {
    console.log('Uploading files:', files);
    setShowUploadModal(false);
  };

  if (!category) {
    // Handle invalid category ID
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-semibold text-gray-900">
            Category Not Found
          </h2>
          <p className="mb-4 text-gray-600">
            The requested knowledge base category does not exist.
          </p>
          <button
            onClick={handleBackToGrid}
            className="rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90"
          >
            Back to Knowledge Base
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex h-[calc(100vh-150px)] flex-col">
        <div className="flex flex-1 overflow-hidden">
          {/* Main Content - Scrollable */}
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 overflow-y-auto">
              <KnowledgeBaseTableView
                category={category}
                onBack={handleBackToGrid}
                onItemClick={handleItemClick}
              />
            </div>
          </div>
        </div>
      </div>

      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
      />
    </>
  );
};

export default KnowledgeBaseCategoryPage;
