import React from 'react';
import { Icons } from '../../../assets/icons/DashboardIcons';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface ActionItemProps {
  action: string;
}

const ActionItem: React.FC<ActionItemProps> = ({ action }) => (
  <div className="flex items-center rounded-lg bg-white p-4">
    <div className="mr-4 flex h-[36px] w-[36px] items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
      <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
    </div>
    <span className="text-xs font-normal text-[#121212] sm:text-sm lg:text-[15px]">
      {action}
    </span>
  </div>
);

const NextActions: React.FC = () => {
  const actions = [
    'Review 12 escalated conversations flagged as high-risk',
    'Authorize <PERSON> to proceed with new negotiation sequence for 18 accounts',
    'Deploy alternate Recura strategy to test better timing for Gen Z segment',
  ];

  return (
    <div className="max-w-[1000px] space-y-4">
      <SectionTitle
        title="Next Actions (Suggested to Human Manager)"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SCYRA_NEXT_ACTIONS}
      />

      <div className="space-y-3">
        {actions.map((action, index) => (
          <ActionItem key={index} action={action} />
        ))}
      </div>
    </div>
  );
};

export default NextActions;
