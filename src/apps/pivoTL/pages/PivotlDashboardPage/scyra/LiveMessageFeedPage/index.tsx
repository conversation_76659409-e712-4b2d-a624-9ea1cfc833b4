import React, { useState } from 'react';
import { Search, Eye, Bell, Check, ChevronLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface MessageRowProps {
  customerId: string;
  lastMessageFrom: string;
  tone: string;
  status: string;
  action: {
    type: 'view' | 'notify' | 'confirm';
    label: string;
  };
}

const MessageRow: React.FC<MessageRowProps> = ({
  customerId,
  lastMessageFrom,
  tone,
  status,
  action,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      // case 'escalated':
      //   return 'text-red-600';
      // case 'commitment sent':
      //   return 'text-green-600';
      // case 'awaiting reply':
      //   return 'text-yellow-600';
      default:
        return 'text-subText';
    }
  };

  const getActionButton = (action: { type: string; label: string }) => {
    const baseClasses =
      'px-3 py-2 text-xs font-medium rounded-full text-[#121212] flex items-center bg-transparent border border-[#121212] ';

    switch (action.type) {
      case 'view':
        return (
          <button className={`${baseClasses} hover:bg-blue-200`}>
            <Eye className="mr-1 h-3 w-3 text-[#FF3E00]" />
            {action.label}
          </button>
        );
      case 'notify':
        return (
          <button className={`${baseClasses} hover:bg-yellow-200`}>
            <Bell className="mr-1 h-3 w-3 text-[#4A35CB]" />
            {action.label}
          </button>
        );
      case 'confirm':
        return (
          <button
            className={`${baseClasses} hover:bg-green-200 hover:border-green-700`}
          >
            <Check className="mr-1 h-3 w-3 text-[#23BD33]" />
            {action.label}
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
        {lastMessageFrom}
      </td>
      <td className="px-6 py-3 text-xs font-normal sm:text-sm lg:text-[15px]">
        {tone}
      </td>
      <td
        className={`px-6 py-3 text-xs font-normal sm:text-sm lg:text-[15px] ${getStatusColor(status)}`}
      >
        {status}
      </td>
      <td className="px-6 py-3 text-xs font-normal sm:text-sm lg:text-[15px]">
        <div className="flex items-center gap-2">
          {getActionButton(action)}
          <ActionDropdown />
        </div>
      </td>
    </tr>
  );
};

const LiveMessageFeedPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const tabs = [
    { id: 'all', label: 'All', count: 156 },
    { id: 'awaiting', label: 'Awaiting Reply', count: 42 },
    { id: 'commitment', label: 'Commitment Pending', count: 18 },
    { id: 'negative', label: 'Negative Sentiment', count: 7 },
    { id: 'escalated', label: 'Escalated', count: 3 },
  ];

  const filterOptions = [
    { id: '1', label: 'CRM System', value: 'crm' },
    { id: '2', label: 'Document Repo', value: 'document' },
    { id: '3', label: 'Emailing Systems', value: 'email' },
    { id: '4', label: 'ERP System', value: 'erp' },
    { id: '5', label: 'Messaging System', value: 'messaging' },
    { id: '6', label: 'Video Conferencing', value: 'video' },
  ];

  const messages = [
    {
      customerId: '#39029',
      lastMessageFrom: 'Scyra',
      tone: 'Neutral',
      status: 'Awaiting reply',
      action: { type: 'view' as const, label: 'View' },
    },
    {
      customerId: '#84401',
      lastMessageFrom: 'Customer',
      tone: 'Negative',
      status: 'Escalated',
      action: { type: 'notify' as const, label: 'Notify' },
    },
    {
      customerId: '#28331',
      lastMessageFrom: 'Customer',
      tone: 'Positive',
      status: 'Commitment Sent',
      action: { type: 'confirm' as const, label: 'Confirm' },
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SCYRA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    // Handle download logic here
    console.log('Downloading report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Reset to first page when changing tabs
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Live Message Feed
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Tabs */}
        <div className="flex w-fit space-x-8 border-b">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`border-b-2 px-1 py-2 text-sm font-medium ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-lg bg-white">
          <div className="overflow-x-auto overflow-y-hidden">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Customer ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Last Message From
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Tone (via Senti)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {messages.map((message, index) => (
                  <MessageRow
                    key={index}
                    customerId={message.customerId}
                    lastMessageFrom={message.lastMessageFrom}
                    tone={message.tone}
                    status={message.status}
                    action={message.action}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default LiveMessageFeedPage;
