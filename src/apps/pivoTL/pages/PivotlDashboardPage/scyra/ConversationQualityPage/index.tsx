import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface MetricRowProps {
  metric: string;
  today: string;
  trend: string;
  notes: string;
}

const MetricRow: React.FC<MetricRowProps> = ({
  metric,
  today,
  trend,
  notes,
}) => (
  <tr className="transition-colors duration-150 hover:bg-gray-50">
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {metric}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {today}
    </td>
    <td
      className={`px-6 py-3 text-xs font-normal sm:text-sm ${
        trend.startsWith('+')
          ? 'text-green-600'
          : trend.startsWith('-')
            ? 'text-red-600'
            : 'text-[#718EBF]'
      }`}
    >
      {trend}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {notes}
    </td>
    <td className="px-6 py-3 text-xs font-normal sm:text-sm lg:text-[15px]">
      <ActionDropdown />
    </td>
  </tr>
);

const ConversationQualityPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'CRM System', value: 'crm' },
    { id: '2', label: 'Document Repo', value: 'document' },
    { id: '3', label: 'Emailing Systems', value: 'email' },
    { id: '4', label: 'ERP System', value: 'erp' },
    { id: '5', label: 'Messaging System', value: 'messaging' },
    { id: '6', label: 'Video Conferencing', value: 'video' },
  ];

  const metrics = [
    {
      metric: 'Open Rate',
      today: '73%',
      trend: '+4%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Response Rate',
      today: '58%',
      trend: '+2%',
      notes: '-',
    },
    {
      metric: 'Positive Sentiment (from Senti)',
      today: '41%',
      trend: '-1%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Commitments Confirmed',
      today: '36',
      trend: '-',
      notes: 'Collin takes over here',
    },
    {
      metric: 'Escalations Triggered',
      today: '8',
      trend: '-',
      notes: 'Routed to human agents or supervisor',
    },
    {
      metric: 'Avg. Response Time (Agent)',
      today: '2.4 min',
      trend: '-',
      notes: 'Human escalation response time',
    },
    {
      metric: 'Open Rate',
      today: '73%',
      trend: '+4%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Response Rate',
      today: '58%',
      trend: '+2%',
      notes: '-',
    },
    {
      metric: 'Positive Sentiment (from Senti)',
      today: '41%',
      trend: '-1%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Commitments Confirmed',
      today: '36',
      trend: '-',
      notes: 'Collin takes over here',
    },
    {
      metric: 'Escalations Triggered',
      today: '8',
      trend: '-',
      notes: 'Routed to human agents or supervisor',
    },
    {
      metric: 'Avg. Response Time (Agent)',
      today: '2.4 min',
      trend: '-',
      notes: 'Human escalation response time',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SCYRA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    // Handle download logic here
    console.log('Downloading report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Conversation Quality & Outcomes
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Metric
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Today
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    7-Day Trend
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Notes
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {metrics.map((metric, index) => (
                  <MetricRow
                    key={index}
                    metric={metric.metric}
                    today={metric.today}
                    trend={metric.trend}
                    notes={metric.notes}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default ConversationQualityPage;
