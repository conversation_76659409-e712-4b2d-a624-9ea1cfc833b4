import React, { useState } from 'react';
import { Search, MoreVertical, ChevronLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { Icons } from '../../../../assets/icons/DashboardIcons';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface ActionItemProps {
  action: string;
  priority?: 'high' | 'medium' | 'low';
}

const ActionItem: React.FC<ActionItemProps> = ({ action }) => {
  return (
    <div
      className={`animate-fadeIn flex items-center justify-between rounded-lg bg-white p-4 transition-all duration-150 hover:border-primary`}
    >
      <div className="flex items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <span className="text-xs font-normal text-subText sm:text-sm">
          {action}
        </span>
      </div>
    </div>
  );
};

const NextActionsPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'High Priority', value: 'high' },
    { id: '2', label: 'Medium Priority', value: 'medium' },
    { id: '3', label: 'Low Priority', value: 'low' },
    { id: '4', label: 'Escalation Required', value: 'escalation' },
    { id: '5', label: 'Review Required', value: 'review' },
  ];

  const actions = [
    {
      action: 'Review 12 escalated conversations flagged as high-risk',
      priority: 'high' as const,
    },
    {
      action:
        'Authorize Collin to proceed with new negotiation sequence for 18 accounts',
      priority: 'medium' as const,
    },
    {
      action:
        'Deploy alternate Recura strategy to test better timing for Gen Z segment',
      priority: 'medium' as const,
    },
    {
      action: 'Update sentiment analysis thresholds based on recent feedback',
      priority: 'low' as const,
    },
    {
      action: 'Schedule training session for new escalation protocols',
      priority: 'low' as const,
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SCYRA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    // Handle download logic here
    console.log('Downloading report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer>
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Next Actions
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
            <button className="flex h-[44px] items-center gap-2 p-4 text-gray-600 hover:text-gray-900">
              <MoreVertical className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Actions List */}
        <div className="mb-8 max-w-[1000px] space-y-4">
          {actions.map((actionItem, index) => (
            <ActionItem
              key={index}
              action={actionItem.action}
              priority={actionItem.priority}
            />
          ))}
        </div>

        {/* Additional Actions */}
        <div className="flex items-center gap-6">
          <button className="flex flex-shrink-0 items-center gap-2 rounded-lg border border-primary bg-[#FFECE3] px-4 py-2 text-sm font-medium text-[#121212] transition-colors hover:bg-primary/10">
            Send Report <Icons.Send className="h-4 w-4" />
          </button>{' '}
          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            onDownload={handleDownload}
            downloadButtonText="Download Report"
          />
        </div>
      </AppContainer>
    </div>
  );
};

export default NextActionsPage;
