import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

const TemplateManagementPanelPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 3;

  const filterOptions = [
    { id: '1', label: 'Active Templates', value: 'active' },
    { id: '2', label: 'Draft Templates', value: 'draft' },
    { id: '3', label: 'Archived Templates', value: 'archived' },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_LIORA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading template management report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Template Management Panel
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search templates"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white p-8">
          <div className="text-center">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Template Management Details
            </h2>
            <p className="text-gray-600">
              Comprehensive template management with version control and usage
              analytics.
            </p>
          </div>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default TemplateManagementPanelPage;
