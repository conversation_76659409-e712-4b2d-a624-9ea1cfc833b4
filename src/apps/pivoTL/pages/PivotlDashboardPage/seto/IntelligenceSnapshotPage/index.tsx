import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface MetricRowProps {
  metric: string;
  currentValue: string;
  trend: 'up' | 'down' | 'stable';
  change: string;
  lastUpdated: string;
  category: string;
}

const MetricRow: React.FC<MetricRowProps> = ({
  metric,
  currentValue,
  trend,
  change,
  lastUpdated,
  category,
}) => {
  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <Icons.TrendUp className="text-green-600 h-4 w-4" />;
      case 'down':
        return <Icons.TrendDown className="h-4 w-4 text-red-600" />;
      case 'stable':
        return <Icons.TrendStable className="h-4 w-4 text-gray-600" />;
      default:
        return null;
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {metric}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {currentValue}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <div className="flex items-center">
          {getTrendIcon(trend)}
          <span
            className={`ml-2 ${
              trend === 'up'
                ? 'text-green-600'
                : trend === 'down'
                  ? 'text-red-600'
                  : 'text-gray-600'
            }`}
          >
            {change}
          </span>
        </div>
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {lastUpdated}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {category}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const IntelligenceSnapshotPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'Performance Metrics', value: 'performance' },
    { id: '2', label: 'Offer Metrics', value: 'offers' },
    { id: '3', label: 'Customer Metrics', value: 'customers' },
    { id: '4', label: 'System Metrics', value: 'system' },
    { id: '5', label: 'Trending Up', value: 'trending_up' },
    { id: '6', label: 'Trending Down', value: 'trending_down' },
  ];

  const metrics = [
    {
      metric: 'Counteroffer Acceptance Rate',
      currentValue: '61%',
      trend: 'up' as const,
      change: '+3%',
      lastUpdated: '2 min ago',
      category: 'Performance',
    },
    {
      metric: 'Offers Proposed by Collin',
      currentValue: '156',
      trend: 'up' as const,
      change: '+12',
      lastUpdated: '5 min ago',
      category: 'Offers',
    },
    {
      metric: 'Offers Originated by Customers',
      currentValue: '89',
      trend: 'stable' as const,
      change: 'No change',
      lastUpdated: '1 min ago',
      category: 'Offers',
    },
    {
      metric: 'Offers via Scyra Outreach',
      currentValue: '234',
      trend: 'down' as const,
      change: '-8',
      lastUpdated: '3 min ago',
      category: 'Offers',
    },
    {
      metric: 'Average Approval Time',
      currentValue: '2.7 sec',
      trend: 'stable' as const,
      change: 'Stable',
      lastUpdated: '1 min ago',
      category: 'System',
    },
    {
      metric: 'Customer Satisfaction Score',
      currentValue: '4.2/5',
      trend: 'up' as const,
      change: '+0.3',
      lastUpdated: '10 min ago',
      category: 'Customer',
    },
    {
      metric: 'Settlement Success Rate',
      currentValue: '73%',
      trend: 'up' as const,
      change: '+5%',
      lastUpdated: '15 min ago',
      category: 'Performance',
    },
    {
      metric: 'AI Confidence Average',
      currentValue: '87%',
      trend: 'down' as const,
      change: '-2%',
      lastUpdated: '8 min ago',
      category: 'System',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETO);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading intelligence snapshot report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Intelligence Snapshot
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search metrics"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7]">
                <tr>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Metric
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Current Value
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Trend
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Last Updated
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Category
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {metrics.map((metric, index) => (
                  <MetricRow
                    key={index}
                    metric={metric.metric}
                    currentValue={metric.currentValue}
                    trend={metric.trend}
                    change={metric.change}
                    lastUpdated={metric.lastUpdated}
                    category={metric.category}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default IntelligenceSnapshotPage;
