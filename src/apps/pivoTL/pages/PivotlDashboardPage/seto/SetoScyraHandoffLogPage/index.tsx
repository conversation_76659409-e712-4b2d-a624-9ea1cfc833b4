import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface HandoffRowProps {
  offerId: string;
  decision: string;
  messageSentBy: string;
  sentimentRisk: 'Low' | 'Medium' | 'High';
  escalationTriggered: boolean;
  timestamp: string;
  customerResponse: string;
}

const HandoffRow: React.FC<HandoffRowProps> = ({
  offerId,
  decision,
  messageSentBy,
  sentimentRisk,
  escalationTriggered,
  timestamp,
  customerResponse,
}) => {
  const getSentimentColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {offerId}
      </td>
      <td className={`px-6 py-3 text-sm lg:text-[15px]`}>{decision}</td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {messageSentBy}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getSentimentColor(sentimentRisk)}`}
        >
          {sentimentRisk}
        </span>
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        {escalationTriggered ? (
          <span className="inline-flex items-center text-red-600">Yes</span>
        ) : (
          <span className="text-green-600 inline-flex items-center">No</span>
        )}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {timestamp}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {customerResponse}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const SetoScyraHandoffLogPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'Accepted Decisions', value: 'accepted' },
    { id: '2', label: 'Countered Decisions', value: 'countered' },
    { id: '3', label: 'Rejected Decisions', value: 'rejected' },
    { id: '4', label: 'High Risk', value: 'high_risk' },
    { id: '5', label: 'Escalated', value: 'escalated' },
    { id: '6', label: 'Recent Messages', value: 'recent' },
  ];

  const handoffs = [
    {
      offerId: 'A1921',
      decision: 'Accepted',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Low' as const,
      escalationTriggered: false,
      timestamp: 'June 15, 2025 10:30 AM',
      customerResponse: 'Positive acknowledgment',
    },
    {
      offerId: 'B1921',
      decision: 'Countered',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Medium' as const,
      escalationTriggered: false,
      timestamp: 'June 15, 2025 09:45 AM',
      customerResponse: 'Requesting clarification',
    },
    {
      offerId: 'D1921',
      decision: 'Rejected',
      messageSentBy: 'Scyra',
      sentimentRisk: 'High' as const,
      escalationTriggered: true,
      timestamp: 'June 15, 2025 09:15 AM',
      customerResponse: 'Aggressive language detected',
    },
    {
      offerId: 'C2234',
      decision: 'Accepted',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Low' as const,
      escalationTriggered: false,
      timestamp: 'June 15, 2025 08:30 AM',
      customerResponse: 'Agreement confirmed',
    },
    {
      offerId: 'E3456',
      decision: 'Countered',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Medium' as const,
      escalationTriggered: false,
      timestamp: 'June 14, 2025 04:20 PM',
      customerResponse: 'Negotiating terms',
    },
    {
      offerId: 'F7890',
      decision: 'Rejected',
      messageSentBy: 'Scyra',
      sentimentRisk: 'High' as const,
      escalationTriggered: true,
      timestamp: 'June 14, 2025 03:45 PM',
      customerResponse: 'Complaint filed',
    },
    {
      offerId: 'G1122',
      decision: 'Accepted',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Low' as const,
      escalationTriggered: false,
      timestamp: 'June 14, 2025 02:15 PM',
      customerResponse: 'Payment scheduled',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETO);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading handoff log report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Seto→Scyra Handoff Log
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search handoffs"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7]">
                <tr>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Offer ID
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Decision
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Message Sent By
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Sentiment Risk
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Escalation Triggered?
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Timestamp
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Customer Response
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {handoffs.map((handoff, index) => (
                  <HandoffRow
                    key={index}
                    offerId={handoff.offerId}
                    decision={handoff.decision}
                    messageSentBy={handoff.messageSentBy}
                    sentimentRisk={handoff.sentimentRisk}
                    escalationTriggered={handoff.escalationTriggered}
                    timestamp={handoff.timestamp}
                    customerResponse={handoff.customerResponse}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default SetoScyraHandoffLogPage;
