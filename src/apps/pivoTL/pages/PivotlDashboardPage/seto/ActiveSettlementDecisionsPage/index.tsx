import React, { useState } from 'react';
import { ChevronLeft, Search, Eye } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface DecisionRowProps {
  offerId: string;
  customerId: string;
  offerAmount: string;
  originalBalance: string;
  settlementPercent: string;
  decision: string;
  confidence: string;
  status: string;
}

const DecisionRow: React.FC<DecisionRowProps> = ({
  offerId,
  customerId,
  offerAmount,
  originalBalance,
  settlementPercent,
  decision,
  confidence,
  status,
}) => {
  const getActionButton = (decision: string) => {
    const baseClasses =
      'px-4 py-1.5 text-xs font-medium rounded-full text-[#121212] flex items-center bg-transparent border border-[#121212] ';

    switch (decision.toLowerCase()) {
      case 'accepted':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-red-100`}>
            <Eye className="mr-1 h-3 w-3 text-red-500" />
            View
          </button>
        );
      case 'countered':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-purple-200`}>
            <Icons.EditIcon className="mr-1 h-3 w-3" />
            Edit
          </button>
        );
      case 'rejected':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-yellow-200`}>
            <Icons.BellNotification className="mr-1 h-3 w-3" />
            Flag
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {offerId}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {offerAmount}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {originalBalance}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {settlementPercent}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {decision}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {confidence}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {status}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        {getActionButton(decision)}
      </td>
    </tr>
  );
};

const ActiveSettlementDecisionsPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'Accepted', value: 'accepted' },
    { id: '2', label: 'Countered', value: 'countered' },
    { id: '3', label: 'Rejected', value: 'rejected' },
    { id: '4', label: 'High Confidence', value: 'high_confidence' },
    { id: '5', label: 'Low Confidence', value: 'low_confidence' },
  ];

  const decisions = [
    {
      offerId: 'A1921',
      customerId: '#44329',
      offerAmount: '$420',
      originalBalance: '$1,200',
      settlementPercent: '35.0%',
      decision: 'Accepted',
      confidence: '94%',
      status: 'Sent via Scyra',
    },
    {
      offerId: 'B0083',
      customerId: '#84401',
      offerAmount: '$800',
      originalBalance: '$2,000',
      settlementPercent: '35.0%',
      decision: 'Countered',
      confidence: '81%',
      status: 'Awaiting reply',
    },
    {
      offerId: 'D1017',
      customerId: '#28331',
      offerAmount: '$300',
      originalBalance: '$5,500',
      settlementPercent: '35.0%',
      decision: 'Rejected',
      confidence: '96%',
      status: 'Escalated',
    },
    // Add more sample data for pagination
    {
      offerId: 'C1234',
      customerId: '#55678',
      offerAmount: '$650',
      originalBalance: '$1,800',
      settlementPercent: '36.1%',
      decision: 'Accepted',
      confidence: '92%',
      status: 'Completed',
    },
    {
      offerId: 'E5567',
      customerId: '#99101',
      offerAmount: '$450',
      originalBalance: '$1,500',
      settlementPercent: '30.0%',
      decision: 'Countered',
      confidence: '88%',
      status: 'Under review',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETO);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading settlement decisions report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Active Settlement Decisions
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7]">
                <tr>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Offer ID
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Customer ID
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Offer Amount
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Original Balance
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Settlement %
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Decision
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Confidence
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Status
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody>
                {decisions.map((decision, index) => (
                  <DecisionRow
                    key={index}
                    offerId={decision.offerId}
                    customerId={decision.customerId}
                    offerAmount={decision.offerAmount}
                    originalBalance={decision.originalBalance}
                    settlementPercent={decision.settlementPercent}
                    decision={decision.decision}
                    confidence={decision.confidence}
                    status={decision.status}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default ActiveSettlementDecisionsPage;
