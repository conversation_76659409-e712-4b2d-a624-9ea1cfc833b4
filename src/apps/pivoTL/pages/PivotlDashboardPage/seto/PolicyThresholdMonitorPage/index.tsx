import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface PolicyRowProps {
  rule: string;
  value: string;
  editable: boolean;
  notes: string;
  lastModified: string;
  modifiedBy: string;
}

const PolicyRow: React.FC<PolicyRowProps> = ({
  rule,
  value,
  editable,
  notes,
  lastModified,
  modifiedBy,
}) => (
  <tr className="transition-colors duration-150 hover:bg-gray-50">
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{rule}</td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{value}</td>
    <td className="px-6 py-3 text-sm lg:text-[15px]">
      {editable ? (
        <span className="text-green-600 inline-flex items-center">Yes</span>
      ) : (
        <span className="inline-flex items-center text-red-600">No</span>
      )}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{notes}</td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {lastModified}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {modifiedBy}
    </td>
    <td className="px-6 py-3 text-sm lg:text-[15px]">
      <ActionDropdown />
    </td>
  </tr>
);

const PolicyThresholdMonitorPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 3;

  const filterOptions = [
    { id: '1', label: 'Editable Rules', value: 'editable' },
    { id: '2', label: 'Read-Only Rules', value: 'readonly' },
    { id: '3', label: 'Recently Modified', value: 'recent' },
    { id: '4', label: 'Violation Alerts', value: 'violations' },
  ];

  const policies = [
    {
      rule: 'Minimum Acceptable Settlement',
      value: '18%',
      editable: true,
      notes: 'Below this = Auto-Reject',
      lastModified: 'June 15, 2025',
      modifiedBy: 'Legal Team',
    },
    {
      rule: 'Max Counter Discount Range',
      value: '20-35%',
      editable: true,
      notes: 'Seto uses this to auto-generate counteroffers',
      lastModified: 'June 10, 2025',
      modifiedBy: 'Operations',
    },
    {
      rule: 'Escalation Trigger Attempts',
      value: '3 consecutive rejections',
      editable: true,
      notes: 'Routes to human manager',
      lastModified: 'June 8, 2025',
      modifiedBy: 'Compliance',
    },
    {
      rule: 'Real-Time Policy Violations Today',
      value: '12',
      editable: false,
      notes: 'Flagged for manual review',
      lastModified: 'Today',
      modifiedBy: 'System',
    },
    {
      rule: 'Maximum Settlement Amount',
      value: '$50,000',
      editable: true,
      notes: 'Above this requires supervisor approval',
      lastModified: 'June 12, 2025',
      modifiedBy: 'Risk Management',
    },
    {
      rule: 'Auto-Approval Confidence Threshold',
      value: '85%',
      editable: true,
      notes: 'Minimum confidence for automated decisions',
      lastModified: 'June 14, 2025',
      modifiedBy: 'AI Team',
    },
    {
      rule: 'Customer Communication Delay',
      value: '24 hours',
      editable: true,
      notes: 'Wait time before follow-up communications',
      lastModified: 'June 9, 2025',
      modifiedBy: 'Customer Success',
    },
    {
      rule: 'High-Risk Account Flag Threshold',
      value: '3 failed payments',
      editable: false,
      notes: 'Automatically flags accounts for special handling',
      lastModified: 'June 5, 2025',
      modifiedBy: 'System',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETO);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading policy threshold report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-[#121212]"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Policy Threshold Monitor
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search policies"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7]">
                <tr>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Rule
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Value
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Editable
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Notes
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Last Modified
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Modified By
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {policies.map((policy, index) => (
                  <PolicyRow
                    key={index}
                    rule={policy.rule}
                    value={policy.value}
                    editable={policy.editable}
                    notes={policy.notes}
                    lastModified={policy.lastModified}
                    modifiedBy={policy.modifiedBy}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default PolicyThresholdMonitorPage;
