import React, { useState, useEffect } from 'react';
import { AvailableAppsGrid } from '../../components/businessStack/AvailableAppsGrid';
import { OutlookOrganisationConnectionModal } from '../../components/businessStack/OutlookOrganisationConnectionModal';
import { OutlookPersonalConnectionModal } from '../../components/businessStack/OutlookPersonalConnectionModal';
import { SharePointConnectionModal } from '../../components/businessStack/SharePointConnectionModal';
import { SalesforceConnectionModal } from '../../components/businessStack/SalesforceConnectionModal';
import { TwilioConnectionModal } from '../../components/businessStack/TwilioConnectionModal';
import { useAvailableAppsApi } from '../../services/businessStackService';
import { BusinessStackPageState } from '../../types/businessStack';
import AppContainer from '../../components/common/AppContainer';
import { Icons } from '../../assets/icons/DashboardIcons';
import ChatSidebar from '../../components/common/ChatSidebar';

const BusinessStackPage: React.FC = () => {
  const [pageState, setPageState] = useState<BusinessStackPageState>({
    availableApps: [],
    isLoadingApps: true,
    searchQuery: '',
    scyraChatState: {
      messages: [],
      isLoading: false,
      sessionId: '',
    },
    currentPage: 1,
    totalPages: 1,
  });

  const [
    outlookOrganisationConnectionModal,
    setOutlookOrganisationConnectionModal,
  ] = useState({
    isOpen: false,
    appName: '',
  });

  const [outlookPersonalModal, setOutlookPersonalModal] = useState({
    isOpen: false,
  });

  const [sharePointModal, setSharePointModal] = useState({
    isOpen: false,
  });

  const [salesforceModal, setSalesforceModal] = useState({
    isOpen: false,
  });

  const [twilioModal, setTwilioModal] = useState({
    isOpen: false,
  });

  const getAvailableApps = useAvailableAppsApi();

  // Load available apps on component mount
  useEffect(() => {
    const loadAvailableApps = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoadingApps: true }));
        const response = await getAvailableApps({
          page: pageState.currentPage,
          size: 10,
          search: pageState.searchQuery,
        });

        setPageState(prev => ({
          ...prev,
          availableApps: response.data.availableApps,
          isLoadingApps: false,
          totalPages: response.data.total,
        }));
      } catch (error) {
        console.error('Error loading available apps:', error);
        setPageState(prev => ({ ...prev, isLoadingApps: false }));
      }
    };

    loadAvailableApps();
  }, [pageState.currentPage, pageState.searchQuery]);

  // Handle search query changes
  const handleSearchChange = (query: string) => {
    setPageState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));
  };

  const handleOpenOutlookOrganisationConnectionModal = (appName: string) => {
    setOutlookOrganisationConnectionModal({
      isOpen: true,
      appName,
    });
  };

  const handleCloseOutlookOrganisationConnectionModal = () => {
    setOutlookOrganisationConnectionModal({
      isOpen: false,
      appName: '',
    });
  };

  const handleOpenOutlookPersonalModal = () => {
    setOutlookPersonalModal({
      isOpen: true,
    });
  };

  const handleCloseOutlookPersonalModal = () => {
    setOutlookPersonalModal({
      isOpen: false,
    });
  };

  const handleOutlookOrganisationConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === outlookOrganisationConnectionModal.appName
          ? { ...app, isConnected: true }
          : app,
      ),
    }));
  };

  const handleOutlookPersonalConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Outlook (Personal)' ? { ...app, isConnected: true } : app,
      ),
    }));
  };

  const handleOpenSharePointModal = () => {
    setSharePointModal({
      isOpen: true,
    });
  };

  const handleCloseSharePointModal = () => {
    setSharePointModal({
      isOpen: false,
    });
  };

  const handleSharePointConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'SharePoint' ? { ...app, isConnected: true } : app,
      ),
    }));
  };

  const handleOpenSalesforceModal = () => {
    setSalesforceModal({
      isOpen: true,
    });
  };

  const handleCloseSalesforceModal = () => {
    setSalesforceModal({
      isOpen: false,
    });
  };

  const handleSalesforceConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Salesforce' ? { ...app, isConnected: true } : app,
      ),
    }));
  };

  const handleOpenTwilioModal = () => {
    setTwilioModal({
      isOpen: true,
    });
  };

  const handleCloseTwilioModal = () => {
    setTwilioModal({
      isOpen: false,
    });
  };

  const handleTwilioConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Twilio' ? { ...app, isConnected: true } : app,
      ),
    }));
  };

  // Handle app connection (for non-modal apps)
  const handleConnectApp = async (appName: string) => {
    // TODO: Implement app connection logic for other apps
    console.log('Connecting to app:', appName);

    // For now, just update the local state to show as connected
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === appName ? { ...app, isConnected: true } : app,
      ),
    }));
  };

  return (
    <div className="flex h-[calc(100vh-150px)] flex-col">
      <div className="flex flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          {/* LHS - Scyra Chat Interface */}
          <ChatSidebar />

          {/* RHS - Available Apps Grid */}
          <div className="flex-1 overflow-y-auto">
            <AppContainer className="space-y-6 p-8 lg:space-y-8">
              <div className="lg:max-w-[800px]">
                <div className="mb-6">
                  <div className="flex items-center gap-3">
                    <div className="flex h-8 w-8 items-center justify-center text-primary">
                      <Icons.Stack className="h-6 w-6" />
                    </div>
                    <h1 className="text-xl font-semibold text-gray-800 lg:text-2xl">
                      Business Stack
                    </h1>
                  </div>
                </div>

                <div className="flex-1 overflow-hidden">
                  <AvailableAppsGrid
                    apps={pageState.availableApps}
                    isLoading={pageState.isLoadingApps}
                    searchQuery={pageState.searchQuery}
                    onSearchChange={handleSearchChange}
                    onConnectApp={handleConnectApp}
                    onOpenOutlookOrganisationModal={
                      handleOpenOutlookOrganisationConnectionModal
                    }
                    onOpenOutlookPersonalModal={handleOpenOutlookPersonalModal}
                    onOpenSharePointModal={handleOpenSharePointModal}
                    onOpenSalesforceModal={handleOpenSalesforceModal}
                    onOpenTwilioModal={handleOpenTwilioModal}
                  />
                </div>
              </div>
            </AppContainer>
          </div>
        </div>

        <OutlookOrganisationConnectionModal
          isOpen={outlookOrganisationConnectionModal.isOpen}
          onClose={handleCloseOutlookOrganisationConnectionModal}
          appName={outlookOrganisationConnectionModal.appName}
          onConnectionSuccess={handleOutlookOrganisationConnectionSuccess}
        />

        <OutlookPersonalConnectionModal
          isOpen={outlookPersonalModal.isOpen}
          onClose={handleCloseOutlookPersonalModal}
          onConnectionSuccess={handleOutlookPersonalConnectionSuccess}
        />

        <SharePointConnectionModal
          isOpen={sharePointModal.isOpen}
          onClose={handleCloseSharePointModal}
          onConnectionSuccess={handleSharePointConnectionSuccess}
        />

        <SalesforceConnectionModal
          isOpen={salesforceModal.isOpen}
          onClose={handleCloseSalesforceModal}
          onConnectionSuccess={handleSalesforceConnectionSuccess}
        />

        <TwilioConnectionModal
          isOpen={twilioModal.isOpen}
          onClose={handleCloseTwilioModal}
          onConnectionSuccess={handleTwilioConnectionSuccess}
        />
      </div>
    </div>
  );
};

export default BusinessStackPage;
