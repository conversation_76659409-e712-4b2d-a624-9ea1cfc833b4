export const contact = '/notification-service/contact';
export const problemStatements = '/upivotal-edu-service/problem-statements';
export const categories = '/upivotal-edu-service/categories';
export const user = '/upivotal-user-service/users';
export const account = '/upivotal-user-service/accounts';
export const mentors = '/upivotal-user-service/mentors';
export const projects = '/upivotal-edu-service/projects';
export const teamMember = '/upivotal-edu-service/team-members';
export const teams = '/upivotal-edu-service/teams';
export const countries = '/upivotal-user-service/countries';
export const universities = '/upivotal-user-service/universities';
export const institutions = '/upivotal-user-service/institutions';
export const goals = '/upivotal-edu-service/goals';
export const objectives = '/upivotal-edu-service/objectives';
export const tasks = '/upivotal-edu-service/tasks';
export const udemyCourses = '/upivotal-edu-service/udemy-courses';
export const wishlist = '/upivotal-edu-service/wish-lists';
export const myCourses = '/upivotal-edu-service/my-courses';
export const courseActivities = '/upivotal-edu-service/course-activities';
export const feedbacks = '/upivotal-user-service/feedbacks';
export const payment = '/billing-service/payments';
export const subscription = '/upivotal-user-service/subscriptions';
export const pricing = '/upivotal-user-service/pricing';
export const courseraCourses = '/upivotal-edu-service/coursera-courses';
export const udacityCourses = '/upivotal-edu-service/udacity-courses';
export const dashboardCount = '/upivotal-edu-service/dashboards/count';
export const chatRoom = '/upivotal-chat-service/chatRoom';
export const chatMessage = '/upivotal-chat-service/chatMessage';
export const conversationChannelMessage =
  '/upivotal-chat-service/conversationChannelMessage';
export const topic = '/upivotal-chat-service/topic';
export const likeTopic = '/upivotal-chat-service/likes/topics';
export const topicComment = '/upivotal-chat-service/topicComment';
export const commentReply = '/upivotal-chat-service/commentReply';
export const conversationChannel = '/upivotal-chat-service/conversationChannel';
export const links = '/upivotal-chat-service/links';
export const notification = '/upivotal-chat-service/notification';
export const grants = '/upivotal-edu-service/grants';
export const grantApplications = '/upivotal-edu-service/grantApplications';
export const uPivotalBenefactor = '/upivotal-user-service/benefactors';
export const uPicks = '/upivotal-edu-service/upicks/public';
export const iacs = '/upivotal-edu-service/iacs/public';
export const userResources = '/upivotal-edu-service/user-resources';
export const uBooks = '/upivotal-edu-service/books';
export const talents = '/upivotal-user-service/talents';
export const universityAdmins = '/upivotal-user-service//university-admins';
export const userAdmins = '/upivotal-user-service/admins';
export const eduAdmins = '/upivotal-edu-service/admins';
export const agenticService = '/upivotal-agentic-service';
export const agenticUserService = '/upivotal-agentic-user-service';
export const launchIt = '/upivotal-agentic-service/ai/launchit';
export const integrationService = '/upivotal-integration-service';
